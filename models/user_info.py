from gino import Gino

db = Gino()

class UserInfo(db.Model):
    __tablename__ = 'tb_user_info'
    __table_args__ = {'schema': 'review'}

    id = db.Column(db.BigInteger(), primary_key=True)
    user_id = db.Column(db.String(50), nullable=False, unique=True)
    account = db.Column(db.String(50), nullable=False, unique=True)
    password = db.Column(db.String(100), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(20), nullable=False, default='user')
    
    # 价格配置字段
    purchase_order_base_price = db.Column(db.Numeric(10, 2), nullable=False, default=0)
    purchase_order_like_price = db.Column(db.Numeric(10, 2), nullable=False, default=0)
    purchase_order_add_on_price = db.Column(db.Numeric(10, 2), nullable=False, default=0)
    purchase_order_review_price = db.Column(db.Numeric(10, 2), nullable=False, default=0)
    unpurchased_order_like_price = db.Column(db.Numeric(10, 2), nullable=False, default=0)
    unpurchased_order_add_on_price = db.Column(db.Numeric(10, 2), nullable=False, default=0)
    fee_discount = db.Column(db.Numeric(10, 2), nullable=False, default=1)
    
    balance = db.Column(db.Numeric(10, 2), nullable=False, default=0)
    
    # 添加创建时间和更新时间
    created_at = db.Column(db.DateTime(timezone=True), server_default=db.func.now())
    updated_at = db.Column(db.DateTime(timezone=True), server_default=db.func.now(), onupdate=db.func.now()) 