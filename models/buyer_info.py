from gino import Gino

db = Gino()

class BuyerInfo(db.Model):
    __tablename__ = 'tb_buyer_info'
    __table_args__ = {'schema': 'review'}

    id = db.Column(db.BigInteger(), primary_key=True)
    birthday = db.Column(db.Date, nullable=True)
    card_body = db.Column(db.String(50), nullable=True)
    card_csv = db.Column(db.String(10), nullable=True)
    card_date = db.Column(db.String(10), nullable=True)
    card_num = db.Column(db.String(50), nullable=True)
    date_issue = db.Column(db.Date, nullable=True)
    device_id = db.Column(db.String(50), nullable=True)
    email = db.Column(db.String(100), nullable=True)
    email_password = db.Column(db.String(100), nullable=True)
    email_url = db.Column(db.String(255), nullable=True)
    fms_code = db.Column(db.String(50), nullable=True)
    fms_name = db.Column(db.String(100), nullable=True)
    is_done = db.Column(db.Boolean, nullable=True)
    name = db.Column(db.String(100), nullable=True)
    parent_name = db.Column(db.String(100), nullable=True)
    passport_number = db.Column(db.String(50), nullable=True)
    passport_url = db.Column(db.String(255), nullable=True)
    phone = db.Column(db.String(20), nullable=True)
    port = db.Column(db.String(20), nullable=True)
    proxy_cn = db.Column(db.String(100), nullable=True)
    proxy_gost = db.Column(db.String(100), nullable=True)
    sex = db.Column(db.String(10), nullable=True)
    surname = db.Column(db.String(100), nullable=True)
    
    # 平台相关字段
    is_seller = db.Column(db.Boolean, default=False)
    is_ozon = db.Column(db.Boolean, default=False)
    pay_ozon = db.Column(db.String(50), nullable=True)
    is_yandex_market = db.Column(db.Boolean, default=False)
    pay_yandex_market = db.Column(db.String(50), nullable=True)
    is_wildberries = db.Column(db.Boolean, default=False)
    pay_wildberries = db.Column(db.String(50), nullable=True)
    is_mail_ru = db.Column(db.Boolean, default=False)
    is_yoo_money = db.Column(db.Boolean, default=False)
    is_telegram = db.Column(db.Boolean, default=False)
    is_whats_app = db.Column(db.Boolean, default=False)
    is_vk = db.Column(db.Boolean, default=False)
    is_sberbank = db.Column(db.Boolean, default=False)
    is_vtb_bank = db.Column(db.Boolean, default=False)
    is_tinkoff_bank = db.Column(db.Boolean, default=False)
    
    # 添加创建时间和更新时间
    created_at = db.Column(db.DateTime(timezone=True), server_default=db.func.now())
    updated_at = db.Column(db.DateTime(timezone=True), server_default=db.func.now(), onupdate=db.func.now()) 