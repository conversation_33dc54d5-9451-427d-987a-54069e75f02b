from gino import Gino
from sqlalchemy.dialects.postgresql import ARRAY

db = Gino()

class OrderInfo(db.Model):
    __tablename__ = 'tb_order_info'
    __table_args__ = {'schema': 'review'}

    id = db.Column(db.<PERSON>Integer(), primary_key=True)
    task_id = db.Column(db.String(50), nullable=False, index=True, comment='任务ID')
    platform = db.Column(db.String(10), nullable=False, comment='平台标识')
    product_id = db.Column(db.BigInteger(), nullable=False, comment='商品ID')
    img = db.Column(db.String(500), nullable=True, comment='商品图片URL')
    price = db.Column(db.Float, nullable=True)
    pre_date = db.Column(db.Date, nullable=True, comment='预定日期')
    order_pcs = db.Column(db.Integer, nullable=False, default=1, comment='订购数量')
    keyword = db.Column(db.String(500), nullable=True, comment='商品关键词')
    is_like = db.Column(db.Boolean, nullable=False, default=False, comment='是否点赞')
    is_cart = db.Column(db.Boolean, nullable=False, default=False, comment='是否加购')
    is_review = db.Column(db.Boolean, nullable=False, default=False, comment='是否评论')
    is_review_text = db.Column(db.Boolean, nullable=False, default=False, comment='是否文字评论')
    is_review_img = db.Column(db.Boolean, nullable=False, default=False, comment='是否图片评论')
    is_review_video = db.Column(db.Boolean, nullable=False, default=False, comment='是否视频评论')
    review_text_input = db.Column(db.Text, nullable=True, comment='评论文字内容')
    review_video_input = db.Column(ARRAY(db.String(100)), nullable=True, comment='评论视频文件名列表')
    review_img_input = db.Column(ARRAY(db.String(100)), nullable=True, comment='评论图片文件名列表')
    es_pay = db.Column(db.Numeric(10, 2), nullable=True, comment='预计支付金额')
    es_cost = db.Column(db.Numeric(10, 2), nullable=True, comment='预计成本')
    user_id = db.Column(db.String(50), nullable=False, comment='用户ID')
    order_id = db.Column(db.String(50), nullable=True, index=True, comment='平台订单ID')
    remark = db.Column(db.Text, nullable=True, comment='备注')
    
    seller_id = db.Column(db.String(50), nullable=True, comment='卖家ID')
    buyer = db.Column(db.String(50), nullable=True, comment='买家账号')
    status = db.Column(db.Integer, nullable=False, default=0, comment='订单状态')
    
    # 添加创建时间和更新时间
    created_at = db.Column(db.DateTime(timezone=True), nullable=False, comment='创建时间')
    updated_at = db.Column(db.DateTime(timezone=True), nullable=False, comment='更新时间')