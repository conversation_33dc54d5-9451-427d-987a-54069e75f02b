from gino import Gino

db = Gino()

class SettlementInfo(db.Model):
    __tablename__ = 'tb_settlement_info'
    __table_args__ = {'schema': 'review'}

    id = db.Column(db.BigInteger(), primary_key=True)
    settlement_id = db.Column(db.String(50), nullable=False, comment='结算编号')
    task_id = db.Column(db.String(50), nullable=False, index=True, comment='任务编号')
    amount = db.Column(db.Numeric(10, 2), nullable=False, comment='结算金额')
    balance = db.Column(db.Numeric(10, 2), nullable=False, comment='结算后余额')
    item_type = db.Column(db.String(50), nullable=False, comment='结算项目类型')
    user_id = db.Column(db.String(50), nullable=False, index=True, comment='用户编号')
    
    # 添加创建时间和更新时间
    created_at = db.Column(db.DateTime(timezone=True), server_default=db.func.now(), comment='创建时间')
    updated_at = db.Column(db.DateTime(timezone=True), server_default=db.func.now(), onupdate=db.func.now(), comment='更新时间') 