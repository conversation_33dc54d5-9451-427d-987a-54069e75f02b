"""
HubStudio Connector 管理类
"""
import os
import subprocess
from typing import Optional, List
import psutil
import time
import logging

class HubStudioConnector:
    """HubStudio Connector 管理类"""
    
    def __init__(
        self,
        client_path: str = None,
        server_mode: str = "http",
        http_port: str = "6873",
        app_id: Optional[str] = None,
        app_secret: Optional[str] = None,
        group_code: Optional[str] = None,
        threads: str = "20",
        timeout: str = "600",
        line_setting: str = "-1",
        remote_debugging: str = "False",
        heartbeat_timeout: str = "180",
        open_wait_sec: str = "180",
        logger: Optional[logging.Logger] = None
    ):
        """
        初始化 Connector 管理器
        
        Args:
            client_path: HubStudio 程序路径
            server_mode: 服务模式,默认 http
            http_port: HTTP 端口,默认 6873
            app_id: 应用 ID
            app_secret: 应用密钥
            group_code: 团队 ID
            threads: 线程数,默认 20
            timeout: 超时时间,默认 600 秒
            line_setting: 线路设置,默认 -1
            remote_debugging: 是否开启远程调试,默认 False
            heartbeat_timeout: 心跳超时时间,默认 180 秒
            open_wait_sec: 打开等待时间,默认 180 秒
            logger: 日志记录器
        """
        # 获取程序路径和工作目录
        if client_path:
            self.client_path = os.path.abspath(client_path)
            self.work_dir = os.path.dirname(self.client_path)
        else:
            self.work_dir = os.getcwd()
            self.client_path = os.path.join(self.work_dir, "hubstudio_connector.exe")
            
        self.server_mode = server_mode
        self.http_port = str(http_port)
        self.app_id = app_id
        self.app_secret = app_secret
        self.group_code = str(group_code) if group_code else None
        self.threads = str(threads)
        self.timeout = float(timeout)  # 转换为浮点数
        self.line_setting = int(line_setting)  # 转换为整数
        self.remote_debugging = str(remote_debugging).lower() == "true"  # 转换为布尔值
        self.heartbeat_timeout = str(heartbeat_timeout)
        self.open_wait_sec = str(open_wait_sec)
        self.logger = logger or logging.getLogger(__name__)
        self.process = None
        
    def _build_command(self) -> str:
        """构建启动命令
        
        Returns:
            拼接好的命令行字符串
        """
        # 检测是否为 PowerShell
        is_powershell = "powershell" in os.environ.get("SHELL", "").lower() or "powershell" in os.environ.get("PSModulePath", "").lower()
        
        # 构建可执行文件路径
        if is_powershell:
            exe_path = ".\\hubstudio_connector.exe"
        else:
            exe_path = "hubstudio_connector.exe"
            
        cmd_parts = []
        
        # 必选参数
        cmd_parts.extend([
            f"--server_mode={self.server_mode}",
            f"--http_port={self.http_port}",
            f"--threads={self.threads}"
        ])
        
        # 可选参数
        if all([self.app_id, self.app_secret, self.group_code]):
            cmd_parts.extend([
                f"--app_id={self.app_id}",
                f"--app_secret={self.app_secret}",
                f"--group_code={self.group_code}"
            ])
            
        if self.remote_debugging:
            cmd_parts.append("--remote_debugging")
            
        if self.line_setting != -1:
            cmd_parts.append(f"--line_setting={self.line_setting}")
            
        if self.timeout != 600:
            cmd_parts.append(f"--timeout={int(self.timeout)}")
            
        if self.heartbeat_timeout != "180":
            cmd_parts.append(f"--heartbeat_timeout={self.heartbeat_timeout}")
            
        return f"{exe_path} {' '.join(cmd_parts)}"
        
    def start(self) -> None:
        """启动 HubStudio Connector"""
        if self.is_running():
            self.logger.info("HubStudio Connector 已经在运行")
            return
        command = self._build_command()
        self.logger.debug(f"执行命令: {command}")
        
        # 用于在线程间通信的标志
        from threading import Event
        self.server_started = Event()
        
        # 使用 gbk 编码（Windows 中文系统默认编码）
        self.process = subprocess.Popen(
            command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            shell=True,
            creationflags=subprocess.CREATE_NO_WINDOW,
            encoding=None,  # 不使用文本模式
            text=False,     # 使用二进制模式
            bufsize=0,      # 不使用缓冲
            cwd=self.work_dir  # 设置工作目录
        )
        
        # 改进的日志读取函数
        def log_output(pipe, is_error=False):
            import io
            try:
                # 使用 io.BufferedReader 来读取
                with io.BufferedReader(pipe) as buffered:
                    while self.process is not None:  # 检查进程是否已被清理
                        try:
                            if self.process.poll() is not None:  # 进程已结束
                                break
                            
                            # 读取一行，包括换行符
                            line = buffered.readline()
                            if not line:  # 没有更多输出
                                time.sleep(0.1)  # 避免CPU过度使用
                                continue
                                
                            try:
                                # 尝试用不同的编码解码
                                line_str = None
                                for encoding in ['gbk', 'cp936', 'utf-8']:
                                    try:
                                        line_str = line.decode(encoding).strip()
                                        if line_str:  # 确保解码后不是空字符串
                                            break
                                    except UnicodeDecodeError:
                                        continue
                                
                                if line_str:
                                    # 过滤掉时间戳前缀，使输出更清晰
                                    if line_str.startswith('20'):  # 时间戳格式如 2025/03/19
                                        try:
                                            # 尝试提取实际消息内容
                                            message = line_str.split(' ', 3)[-1]
                                        except:
                                            message = line_str
                                    else:
                                        message = line_str
                                        
                                    # 检查是否包含服务器启动成功的消息
                                    if "starting http server at:" in line_str:
                                        self.server_started.set()
                                        
                                    # 只打印到控制台，不重复记录到logger
                                    if is_error:
                                        print(f"\033[91m[ERROR] {message}\033[0m")  # 红色错误信息
                                    else:
                                        print(f"\033[92m[INFO] {message}\033[0m")   # 绿色普通信息
                                            
                            except Exception as decode_err:
                                print(f"\033[91m[ERROR] 解码输出时发生错误: {str(decode_err)}\033[0m")
                                
                        except (OSError, IOError) as e:
                            print(f"\033[91m[ERROR] 读取输出时发生错误: {str(e)}\033[0m")
                            break
            except Exception as e:
                if self.process is not None:  # 只在进程仍然存在时记录错误
                    print(f"\033[91m[ERROR] 日志读取错误: {str(e)}\033[0m")
        
        from threading import Thread
        self.stdout_thread = Thread(target=log_output, args=(self.process.stdout,), daemon=True)
        self.stderr_thread = Thread(target=log_output, args=(self.process.stderr, True), daemon=True)
        self.stdout_thread.start()
        self.stderr_thread.start()
        
        # 等待服务启动
        start_time = time.time()
        while time.time() - start_time < self.timeout:
            if not self.is_running():
                self.logger.error("HubStudio Connector 进程已退出")
                raise RuntimeError("进程意外退出")
                
            if self.server_started.wait(1):  # 等待服务器启动消息，每秒检查一次
                return
                
        self.logger.error("HubStudio Connector 启动超时")
        self.stop()  # 启动超时时确保清理资源
        raise TimeoutError("启动超时")
        
    def stop(self) -> None:
        """停止 HubStudio Connector"""
        if not self.is_running():
            return

        # 先终止进程
        if self.process:
            try:
                self.process.terminate()
                self.process.wait(timeout=5)
            except Exception as e:
                self.logger.warning(f"终止进程时发生错误: {str(e)}")
            finally:
                self.process = None
            
        # 确保进程完全停止
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                if proc.info['name'] == 'hubstudio_connector.exe':
                    proc.terminate()
                    proc.wait(timeout=5)
            except (psutil.NoSuchProcess, psutil.TimeoutExpired, Exception) as e:
                self.logger.warning(f"清理进程时发生错误: {str(e)}")
                continue
        
        # 等待日志线程结束
        time.sleep(1)
        
    def is_running(self) -> bool:
        """检查 HubStudio Connector 是否正在运行
        
        Returns:
            是否正在运行
        """
        if self.process and self.process.poll() is None:
            return True
            
        for proc in psutil.process_iter(['pid', 'name']):
            if proc.info['name'] == 'hubstudio_connector.exe':
                return True
                
        return False
        
    def __enter__(self):
        """上下文管理器入口"""
        self.start()
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.stop() 