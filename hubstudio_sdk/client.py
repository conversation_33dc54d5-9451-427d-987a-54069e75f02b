"""
HubStudio SDK 客户端类
"""
import json
import requests
from typing import List, Dict, Optional, Union, Tuple, Any
from playwright.sync_api import sync_playwright
from playwright.async_api import async_playwright
from .models import (
    Environment, 
    BrowserStatus,
    Group,
    Cookie,
    AdvancedFingerprint,
    Screen
)
from .exceptions import HubStudioError
from .connector import HubStudioConnector
from logs import setup_logger

class HubStudioClient:
    """HubStudio API 客户端"""
    
    def __init__(
        self,
        host: str = "localhost",
        port: int = 6873,
        app_id: Optional[str] = None,
        app_secret: Optional[str] = None,
        group_code: Optional[str] = None,
        client_path: Optional[str] = None,
        auto_start: bool = True,
        config: Optional[Dict[str, Any]] = None,
        log_level: str = "INFO",
        log_file: Optional[str] = None,
        **connector_kwargs
    ):
        """
        初始化 HubStudio 客户端
        
        Args:
            host: API 主机地址,默认为 localhost
            port: API 端口,默认为 6873
            app_id: 应用 ID
            app_secret: 应用密钥  
            group_code: 团队 ID
            client_path: HubStudio 程序路径
            auto_start: 是否自动启动 Connector,默认 True
            config: 配置字典，用于覆盖默认配置
            log_level: 日志级别
            log_file: 日志文件路径
            **connector_kwargs: 其他 Connector 参数
        """
        # 使用传入的配置或创建默认配置
        self.config = config or {}
        
        # 配置优先级: 参数 > 配置字典
        self.base_url = f"http://{host or self.config.get('host', 'localhost')}"
        self.port = port or self.config.get('http_port', 6873)
        self.base_url = f"{self.base_url}:{self.port}"
        
        self.app_id = app_id or self.config.get('app_id')
        self.app_secret = app_secret or self.config.get('app_secret')
        self.group_code = group_code or self.config.get('group_code')
        
        # 设置日志
        self.logger = setup_logger(
            "hubstudio",
            log_level or self.config.get('log_level', 'INFO'),
            log_file or self.config.get('log_file')
        )
        
        self.session = requests.Session()
        
        # 合并 connector 配置
        connector_config = self.config.get('connector', {})
        connector_config.update(connector_kwargs)
        
        # 初始化 Connector
        self.connector = HubStudioConnector(
            client_path=client_path or self.config.get('client_path'),
            http_port=self.port,
            app_id=self.app_id,
            app_secret=self.app_secret,
            group_code=self.group_code,
            logger=self.logger,
            **connector_config
        )
        
        if auto_start or self.config.get('auto_start', True):
            self.start()
        
    def start(self) -> None:
        """启动 HubStudio 服务并自动登录"""
        self.connector.start()
        self.login()
        
    def stop(self) -> None:
        """停止 HubStudio 服务"""
        self.quit()
        
    def is_running(self) -> bool:
        """检查 HubStudio 服务是否正在运行
        
        Returns:
            bool: 服务是否正在运行
        """
        return self.connector.is_running()
        
    def __enter__(self):
        """上下文管理器入口"""
        self.start()  # 现在start方法会自动调用login
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.stop()
        
    def _request(
        self, 
        method: str,
        path: str,
        data: Optional[Dict] = None,
        params: Optional[Dict] = None
    ) -> Dict:
        """发送 HTTP 请求
        
        Args:
            method: HTTP 方法
            path: API 路径
            data: POST 数据
            params: URL 参数
            
        Returns:
            API 完整响应数据
            
        Raises:
            HubStudioError: API 调用失败
        """
        url = f"{self.base_url}{path}"
        
        try:
            if method == "GET":
                response = self.session.get(url, params=params)
            else:
                response = self.session.post(url, json=data)
                
            response.raise_for_status()
            result = response.json()
            
            if result["code"] != 0:
                raise HubStudioError(
                    f"API调用失败: {result['msg']}",
                    code=result["code"]
                )
                
            return result
            
        except requests.exceptions.RequestException as e:
            raise HubStudioError(f"请求失败: {str(e)}")
            
    def login(self) -> None:
        """登录 HubStudio
        
        使用 app_id、app_secret 和 group_code 登录
        
        Raises:
            HubStudioError: 登录失败
        """
        if not all([self.app_id, self.app_secret, self.group_code]):
            raise HubStudioError("请提供 app_id、app_secret 和 group_code")
            
        data = {
            "appId": self.app_id,
            "appSecret": self.app_secret,
            "groupCode": self.group_code
        }
        
        self._request("POST", "/login", data=data)
        
    def quit(self) -> None:
        """退出 HubStudio"""
        return self._request("POST", "/quit")
        
    def get_environments(
        self,
        container_codes: Optional[List[int]] = None,
        container_name: Optional[str] = None,
        tag_names: Optional[List[str]] = None,
        current: int = 1,
        size: int = 200
    ) -> List[Environment]:
        """获取环境列表
        
        Args:
            container_codes: 环境 ID 列表
            container_name: 环境名称
            tag_names: 分组名称列表
            current: 当前页码
            size: 每页数量
            
        Returns:
            环境列表
        """
        data = {
            "current": current,
            "size": size
        }
        
        if container_codes:
            data["containerCodes"] = container_codes
        if container_name:
            data["containerName"] = container_name
        if tag_names:
            data["tagNames"] = tag_names
            
        result = self._request("POST", "/api/v1/env/list", data=data)
        return [Environment(**env) for env in result["data"]["list"]]
        
    def create_environment(
        self,
        container_name: str,
        as_dynamic_type: int = 1,
        proxy_type_name: str = "不使用代理",
        tag_name: Optional[str] = None,
        remark: Optional[str] = None,
        cookie: Optional[str] = None,
        advanced_bo: Optional[AdvancedFingerprint] = None,
        **kwargs
    ) -> Environment:
        """创建新环境
        
        Args:
            container_name: 环境名称
            as_dynamic_type: IP变更提醒,1-静态,2-动态
            proxy_type_name: 代理类型
            tag_name: 分组名称
            remark: 备注信息
            cookie: Cookie 字符串
            advanced_bo: 高级指纹参数
            **kwargs: 其他参数
            
        Returns:
            创建的环境信息
        """
        data = {
            "containerName": container_name,
            "asDynamicType": as_dynamic_type,
            "proxyTypeName": proxy_type_name,
            **kwargs
        }
        
        if tag_name:
            data["tagName"] = tag_name
        if remark:
            data["remark"] = remark
        if cookie:
            data["cookie"] = cookie
        if advanced_bo:
            data["advancedBo"] = advanced_bo.dict()
            
        result = self._request("POST", "/api/v1/env/create", data=data)
        return Environment(**result)
        
    def update_environment(
        self,
        container_code: int,
        container_name: str,
        tag_name: str,
        core_version: int,
        **kwargs
    ) -> bool:
        """更新环境
        
        Args:
            container_code: 环境 ID
            container_name: 环境名称
            tag_name: 分组名称
            core_version: 内核版本
            **kwargs: 其他参数
            
        Returns:
            是否更新成功
        """
        data = {
            "containerCode": container_code,
            "containerName": container_name,
            "tagName": tag_name,
            "coreVersion": core_version,
            **kwargs
        }
        
        return self._request("POST", "/api/v1/env/update", data=data)
        
    def delete_environments(self, container_codes: List[int]) -> bool:
        """删除环境
        
        Args:
            container_codes: 环境 ID 列表
            
        Returns:
            是否删除成功
        """
        data = {"containerCodes": container_codes}
        return self._request("POST", "/api/v1/env/del", data=data)
        
    def start_browser(
        self,
        container_code: str,
        is_headless: bool = False,
        is_webdriver_readonly: bool = False,
        container_tabs: Optional[List[str]] = None,
        args: Optional[List[str]] = None
    ) -> Dict:
        """启动浏览器
        
        Args:
            container_code: 环境 ID
            is_headless: 是否无头模式
            is_webdriver_readonly: 是否只读模式
            container_tabs: 启动 URL 列表
            args: 启动参数
            
        Returns:
            浏览器信息，包含debuggingPort
        """
        data = {
            "containerCode": container_code,
            "isHeadless": is_headless,
            "isWebDriverReadOnlyMode": is_webdriver_readonly
        }
        
        if container_tabs:
            data["containerTabs"] = container_tabs
        if args:
            data["args"] = args
            
        result = self._request("POST", "/api/v1/browser/start", data=data)
        return result

    def start_playwright(
        self,
        container_code: str,
        is_headless: bool = False,
        container_tabs: Optional[List[str]] = None,
        args: Optional[List[str]] = None
    ) -> Tuple[Dict, object]:
        """启动浏览器并返回同步Playwright浏览器实例
        
        Args:
            container_code: 环境 ID
            is_headless: 是否无头模式
            container_tabs: 启动 URL 列表
            args: 启动参数
            
        Returns:
            Tuple[Dict, object]: (浏览器信息, Playwright浏览器实例)
        """
        # 启动HubStudio浏览器并获取debugging port
        browser_info = self.start_browser(
            container_code=container_code,
            is_headless=is_headless,
            container_tabs=container_tabs,
            args=args
        )
        
        if "data" not in browser_info or "debuggingPort" not in browser_info["data"]:
            raise HubStudioError("未能获取到debugging port")
            
        debugging_port = browser_info["data"]["debuggingPort"]
        
        # 启动Playwright
        playwright = sync_playwright().start()
        browser = playwright.chromium.connect_over_cdp(f"http://localhost:{debugging_port}")
        
        return browser_info, browser

    async def start_playwright_async(
        self,
        container_code: str,
        is_headless: bool = False,
        container_tabs: Optional[List[str]] = None,
        args: Optional[List[str]] = None
    ) -> Tuple[Dict, object]:
        """启动浏览器并返回异步Playwright浏览器实例
        
        Args:
            container_code: 环境 ID
            is_headless: 是否无头模式
            container_tabs: 启动 URL 列表
            args: 启动参数
            
        Returns:
            Tuple[Dict, object]: (浏览器信息, Playwright浏览器实例)
        """
        # 启动HubStudio浏览器并获取debugging port
        browser_info = self.start_browser(
            container_code=container_code,
            is_headless=is_headless,
            container_tabs=container_tabs,
            args=args
        )
        
        if "data" not in browser_info or "debuggingPort" not in browser_info["data"]:
            raise HubStudioError("未能获取到debugging port")
            
        debugging_port = browser_info["data"]["debuggingPort"]
        
        # 启动异步Playwright
        playwright = await async_playwright().start()
        browser = await playwright.chromium.connect_over_cdp(f"http://localhost:{debugging_port}")
        
        return browser_info, browser
        
    def stop_browser(self, container_code: str) -> bool:
        """关闭浏览器
        
        Args:
            container_code: 环境 ID
            
        Returns:
            是否关闭成功
        """
        return self._request(
            "POST",
            "/api/v1/browser/stop",
            data={"containerCode": container_code}
        )
        
    def get_browser_status(
        self,
        container_codes: List[str]
    ) -> List[BrowserStatus]:
        """获取浏览器状态
        
        Args:
            container_codes: 环境 ID 列表
            
        Returns:
            浏览器状态列表
        """
        data = {"containerCodes": container_codes}
        result = self._request(
            "POST",
            "/api/v1/browser/all-browser-status",
            data=data
        )
        return [BrowserStatus(**status) for status in result["containers"]]
        
    def get_groups(self) -> List[Group]:
        """获取分组列表
        
        Returns:
            分组列表
        """
        result = self._request("POST", "/api/v1/group/list")
        return [Group(**group) for group in result]
        
    def create_group(self, tag_name: str) -> bool:
        """创建分组
        
        Args:
            tag_name: 分组名称
            
        Returns:
            是否创建成功
        """
        data = {"tagName": tag_name}
        return self._request("POST", "/api/v1/group/create", data=data)
        
    def delete_group(self, tag_code: str) -> bool:
        """删除分组
        
        Args:
            tag_code: 分组 ID
            
        Returns:
            是否删除成功
        """
        data = {"tagCode": tag_code}
        return self._request("POST", "/api/v1/group/del", data=data)
        
    def import_cookie(
        self,
        container_code: Union[str, int],
        cookie: str
    ) -> bool:
        """导入 Cookie
        
        Args:
            container_code: 环境 ID
            cookie: Cookie 字符串
            
        Returns:
            是否导入成功
        """
        data = {
            "containerCode": str(container_code),
            "cookie": cookie
        }
        return self._request("POST", "/api/v1/env/import-cookie", data=data)
        
    def export_cookie(self, container_code: Union[str, int]) -> List[Cookie]:
        """导出 Cookie
        
        Args:
            container_code: 环境 ID
            
        Returns:
            Cookie 列表
        """
        data = {"containerCode": str(container_code)}
        result = self._request("POST", "/api/v1/env/export-cookie", data=data)
        cookies = json.loads(result)
        return [Cookie(**cookie) for cookie in cookies]
        
    def clear_cache(self, browser_oauths: Optional[List[str]] = None) -> Dict:
        """清除环境本地缓存
        
        Args:
            browser_oauths: 浏览器 ID 列表,不传则清除所有缓存
            
        Returns:
            清理结果
        """
        data = {}
        if browser_oauths:
            data["browserOauths"] = browser_oauths
            
        return self._request("POST", "/api/v1/cache/clear", data=data)
        
    def reset_extension(self, browser_oauth: str, plugin_ids: List[str]) -> bool:
        """清理环境内插件缓存
        
        Args:
            browser_oauth: 浏览器 ID
            plugin_ids: 插件 ID 列表
            
        Returns:
            是否清理成功
        """
        data = {
            "browserOauth": browser_oauth,
            "pluginIds": plugin_ids
        }
        return self._request("POST", "/api/v1/browser/reset-extension", data=data)
        
    def download_core(self, cores: List[Dict[str, Union[int, str]]]) -> bool:
        """下载环境内核
        
        Args:
            cores: 内核配置列表,每项包含:
                  - browserType: 浏览器类型,1-Chrome,2-Firefox
                  - version: 版本号
                  
        Returns:
            是否下载成功
        """
        data = {"Cores": cores}
        return self._request("POST", "/api/v1/browser/download-core", data=data)
        
    def refresh_fingerprint(
        self,
        container_code: int,
        ua_version: Optional[int] = None,
        core_version: Optional[int] = None,
        type: str = "windows"
    ) -> bool:
        """刷新指纹
        
        Args:
            container_code: 环境 ID
            ua_version: UA 版本,不传则随机最新 UA
            core_version: 内核版本,不传则不改变
            type: 操作系统类型,默认 windows
            
        Returns:
            是否刷新成功
        """
        data = {
            "containerCode": container_code,
            "type": type
        }
        
        if ua_version:
            data["uaVersion"] = ua_version
        if core_version:
            data["coreVersion"] = core_version
            
        return self._request("POST", "/api/v1/env/refresh-fingerprint", data=data)
        
    def get_displays(self) -> List[Screen]:
        """获取全部屏幕
        
        Returns:
            屏幕列表
        """
        result = self._request("POST", "/api/v1/display/all")
        return [Screen(**screen) for screen in result["screens"]]
        
    def arrange_windows(
        self,
        x: Optional[int] = None,
        y: Optional[int] = None,
        width: Optional[int] = None,
        height: Optional[int] = None,
        gap_x: Optional[int] = None,
        gap_y: Optional[int] = None,
        col_num: Optional[int] = None,
        screen_id: Optional[int] = None
    ) -> bool:
        """浏览器窗口自定义排列
        
        Args:
            x: 起始位置x坐标,默认10
            y: 起始位置y坐标,默认10
            width: 窗口宽度,默认600
            height: 窗口高度,默认500
            gap_x: 窗口横向间距,默认20
            gap_y: 窗口纵向间距,默认20
            col_num: 每行展示窗口数量,默认3
            screen_id: 屏幕ID
            
        Returns:
            是否排列成功
        """
        data = {}
        if x is not None:
            data["x"] = x
        if y is not None:
            data["y"] = y
        if width is not None:
            data["width"] = width
        if height is not None:
            data["height"] = height
        if gap_x is not None:
            data["gapX"] = gap_x
        if gap_y is not None:
            data["gapY"] = gap_y
        if col_num is not None:
            data["colNum"] = col_num
        if screen_id is not None:
            data["screenId"] = screen_id
            
        return self._request("POST", "/api/v1/browser/arrange", data=data)
        
    def stop_all_browsers(self, clear_opening: bool = False) -> bool:
        """关闭所有环境
        
        Args:
            clear_opening: 是否清空启动环境队列
            
        Returns:
            是否关闭成功
        """
        data = {"clearOpening": clear_opening}
        return self._request("POST", "/api/v1/browser/stop-all", data=data)
        
    def get_random_ua(
        self,
        type: str = "windows",
        phone_model: Optional[str] = None,
        version: Optional[List[int]] = None
    ) -> str:
        """获取随机 UA
        
        Args:
            type: 操作系统类型,默认 windows
            phone_model: 手机型号,type 为 android/ios 时必填
            version: UA 版本列表,不传则随机
            
        Returns:
            UA 字符串
        """
        data = {"type": type}
        
        if phone_model:
            data["phoneModel"] = phone_model
        if version:
            data["version"] = version
            
        return self._request("POST", "/api/v1/env/random-ua", data=data) 