"""
HubStudio SDK 数据模型
"""
from typing import List, Optional
from pydantic import BaseModel, Field
from datetime import datetime

class Environment(BaseModel):
    """环境信息"""
    container_code: Optional[int] = Field(None, alias="containerCode")
    container_name: Optional[str] = Field(None, alias="containerName")
    create_time: Optional[datetime] = Field(None, alias="createTime")
    tag_name: Optional[str] = Field(None, alias="tagName")
    tag_code: Optional[str] = Field(None, alias="tagCode")
    remark: Optional[str] = None
    proxy_type_name: Optional[str] = Field(None, alias="proxyTypeName")
    proxy_host: Optional[str] = Field(None, alias="proxyHost")
    proxy_port: Optional[int] = Field(None, alias="proxyPort")
    last_used_ip: Optional[str] = Field(None, alias="lastUsedIp")
    last_country: Optional[str] = Field(None, alias="lastCountry")
    last_region: Optional[str] = Field(None, alias="lastRegion")
    last_city: Optional[str] = Field(None, alias="lastCity")
    open_time: Optional[datetime] = Field(None, alias="openTime")
    all_open_time: Optional[str] = Field(None, alias="allOpenTime")
    as_dynamic_type: Optional[int] = Field(None, alias="asDynamicType")
    ip_database_channel: Optional[int] = Field(None, alias="ipDatabaseChannel")
    ip_protocol_type: Optional[int] = Field(None, alias="ipProtocolType")
    core_version: Optional[int] = Field(None, alias="coreVersion")
    reference_ip: Optional[str] = Field(None, alias="referenceIp")
    
    class Config:
        """Pydantic 配置"""
        allow_population_by_field_name = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
        # 允许额外字段
        extra = "allow"
        # 自动生成别名
        alias_generator = lambda x: "".join(word.capitalize() if i > 0 else word for i, word in enumerate(x.split("_")))

class BrowserStatus(BaseModel):
    """浏览器状态"""
    container_code: str
    status: int  # 1-开启中 0-已开启 2-关闭中 3-已关闭

class Group(BaseModel):
    """环境分组"""
    tag_name: str
    tag_code: str

class Cookie(BaseModel):
    """Cookie 信息"""
    name: str
    value: str
    domain: str
    path: str
    secure: bool
    http_only: bool
    persistent: str
    creation: datetime
    last_access: datetime
    expires: Optional[datetime] = None
    priority: str
    has_expires: str
    samesite: str
    source_scheme: str
    firstpartyonly: str
    scheme_map: bool
    is_self: bool

class AdvancedFingerprint(BaseModel):
    """高级指纹参数"""
    ua_version: Optional[str] = None
    ua: Optional[str] = None
    language_type: Optional[int] = None
    languages: Optional[List[str]] = None
    gmt: Optional[str] = None
    geography: Optional[str] = None
    geo_tips: Optional[int] = None
    geo_rule: Optional[int] = None
    longitude: Optional[str] = None
    latitude: Optional[str] = None
    radius: Optional[int] = None
    height: Optional[int] = None
    width: Optional[int] = None
    fonts_type: Optional[int] = None
    fonts: Optional[List[str]] = None
    font_fingerprint: Optional[int] = None
    web_rtc: Optional[int] = None
    web_rtc_local_ip: Optional[str] = None
    canvas: Optional[int] = None
    webgl: Optional[int] = None
    web_gpu: Optional[int] = None
    hardware_acceleration: Optional[int] = None
    webgl_info: Optional[int] = None
    audio_context: Optional[int] = None
    speech_voices: Optional[int] = None
    media: Optional[int] = None
    cpu: Optional[int] = None
    memory: Optional[int] = None
    do_not_track: Optional[int] = None
    battery: Optional[int] = None
    port_scan: Optional[int] = None
    white_list: Optional[str] = None

    class Config:
        """Pydantic 配置"""
        allow_population_by_field_name = True
        alias_generator = lambda x: x.replace("_", "")

class Screen(BaseModel):
    """屏幕信息"""
    id: int
    width: int
    height: int
    real_width: int
    real_height: int
    x: int
    y: int
    scale_factor: float
    current: bool
    internal: bool
    is_primary_screen: bool

    class Config:
        """Pydantic 配置"""
        allow_population_by_field_name = True
        alias_generator = lambda x: x.replace("_", "") 