import sys
import os
import time
import requests
import psutil
import logging
import win32gui
import win32process
import win32con
import win32api
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QPushButton,
                           QLabel, QTextEdit, QMessageBox, QGroupBox,
                           QHBoxLayout, QListWidget, QListWidgetItem,
                           QComboBox, QDialog, QLineEdit, QFormLayout,
                           QDialogButtonBox)
from PyQt6.QtCore import QThread, pyqtSignal, QProcess, Qt, QTimer
import json
from hubstudio_sdk.client import HubStudioClient
from hubstudio_sdk.exceptions import HubStudioError

# 创建自定义的日志处理器
class QTextEditHandler(logging.Handler):
    def __init__(self, signal):
        super().__init__()
        self.signal = signal
        self.setFormatter(logging.Formatter('%(levelname)s: %(message)s'))

    def emit(self, record):
        msg = self.format(record)
        # 根据日志级别添加前缀
        level_prefix = {
            'DEBUG': '[调试]',
            'INFO': '[信息]',
            'WARNING': '[警告]',
            'ERROR': '[错误]',
            'CRITICAL': '[严重]'
        }.get(record.levelname, '[信息]')
        self.signal.emit(f"{level_prefix} {record.message}")

class ConsoleLogThread(QThread):
    """用于监控控制台输出的线程"""
    log_received = pyqtSignal(str)
    
    def __init__(self, pid):
        super().__init__()
        self.pid = pid
        self.running = True
    
    def run(self):
        try:
            # 查找进程的主窗口
            def callback(hwnd, hwnds):
                try:
                    _, found_pid = win32process.GetWindowThreadProcessId(hwnd)
                    if found_pid == self.pid:
                        # 获取窗口标题
                        title = win32gui.GetWindowText(hwnd)
                        # 检查是否为Python或HubStudio的控制台窗口
                        if "python" in title.lower() or "hubstudio" in title.lower():
                            hwnds.append(hwnd)
                except:
                    pass
                return True
            
            hwnds = []
            while self.running and not hwnds:
                win32gui.EnumWindows(callback, hwnds)
                if not hwnds:
                    self.msleep(100)
            
            if not self.running or not hwnds:
                return
                
            hwnd = hwnds[0]
            last_text = ""
            
            # 获取控制台窗口的文本
            while self.running:
                try:
                    # 获取窗口文本长度
                    length = win32gui.SendMessage(hwnd, win32con.WM_GETTEXTLENGTH, 0, 0)
                    if length > 0:
                        # 分配缓冲区
                        buffer = win32gui.PyMakeBuffer(length + 1)
                        # 获取窗口文本
                        win32gui.SendMessage(hwnd, win32con.WM_GETTEXT, length + 1, buffer)
                        current_text = str(buffer[:length])
                        
                        # 找出新增的文本
                        if current_text != last_text:
                            # 如果是第一次获取文本，发送所有行
                            if not last_text:
                                lines = current_text.split('\n')
                            else:
                                # 否则只发送新增的行
                                lines = current_text[len(last_text):].split('\n')
                            
                            for line in lines:
                                line = line.strip()
                                if line:  # 发送所有非空行
                                    self.log_received.emit(line)
                            
                            last_text = current_text
                    
                except Exception as e:
                    print(f"读取控制台输出错误: {str(e)}")
                
                self.msleep(100)
                
        except Exception as e:
            print(f"控制台监控线程错误: {str(e)}")
    
    def stop(self):
        self.running = False

class ServiceThread(QThread):
    status_updated = pyqtSignal(str)
    error_occurred = pyqtSignal(str)
    log_received = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.client = None
        self.running = False
        self.process = None
        self.process_id = None
        self.console_thread = None
        
        # 设置日志处理
        self.logger = logging.getLogger('hubstudio')
        self.logger.setLevel(logging.INFO)
        
        # 设置日志格式
        formatter = logging.Formatter('%(message)s')
        
        # 添加控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
        # 添加自定义处理器用于GUI显示
        gui_handler = QTextEditHandler(self.log_received)
        gui_handler.setFormatter(formatter)
        self.logger.addHandler(gui_handler)
        
        # 设置hubstudio_sdk的日志
        sdk_logger = logging.getLogger('hubstudio_sdk')
        sdk_logger.setLevel(logging.INFO)
        sdk_logger.addHandler(console_handler)
        sdk_logger.addHandler(gui_handler)
    
    def is_process_running(self, process_name="hubstudio_connector.exe"):
        """检查进程是否在运行"""
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                if proc.info['name'].lower() == process_name.lower():
                    return proc.info['pid']
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        return None

    def check_server_status(self, host, port, timeout=30):
        """检查服务器是否启动"""
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                # 尝试多个可能的API端点
                endpoints = ['/status', '/api/status', '/']
                for endpoint in endpoints:
                    try:
                        url = f"http://{host}:{port}{endpoint}"
                        self.log_received.emit(f"[调试] 尝试连接: {url}")
                        response = requests.get(url, timeout=1)
                        if response.status_code == 200:
                            return True
                    except requests.RequestException:
                        continue
            except Exception as e:
                self.log_received.emit(f"[调试] 连接失败: {str(e)}")
            self.msleep(1000)  # 增加等待时间到1秒
        return False
    
    def run(self):
        try:
            self.log_received.emit("[信息] 正在创建HubStudio客户端...")
            
            # 创建客户端
            self.client = HubStudioClient(config_file="config.json")
            
            # 检查进程是否启动并获取日志
            self.process_id = self.is_process_running()
            if not self.process_id:
                raise Exception("HubStudio服务未正常启动")
            
            # 启动控制台日志捕获线程
            self.console_thread = ConsoleLogThread(self.process_id)
            self.console_thread.log_received.connect(lambda msg: self.log_received.emit(msg))
            self.console_thread.start()
            
            self.running = True
            self.status_updated.emit("服务已启动")
            
            # 持续监控进程状态
            while self.running:
                if not self.is_process_running():
                    if not self.running:  # 如果是正常停止，不报错
                        break
                    raise Exception("HubStudio客户端已停止运行")
                self.msleep(1000)
                
        except Exception as e:
            self.error_occurred.emit(str(e))
            self.running = False
        finally:
            if self.console_thread:
                self.console_thread.stop()
                self.console_thread.wait()
    
    def stop(self):
        """停止服务线程"""
        try:
            if self.client:
                self.log_received.emit("[信息] 正在关闭HubStudio服务...")
                self.client.quit()  # 使用SDK的quit方法关闭服务
                
                # 等待进程实际退出
                start_time = time.time()
                while time.time() - start_time < 10:  # 最多等待10秒
                    if not self.is_process_running():
                        self.log_received.emit("[信息] HubStudio服务已成功关闭")
                        break
                    time.sleep(0.5)
                else:
                    self.log_received.emit("[警告] 等待服务关闭超时")
                
                self.client = None
                
                # 停止控制台监控线程
                if self.console_thread:
                    self.console_thread.stop()
                    self.console_thread.wait()
                    
        except Exception as e:
            self.log_received.emit(f"[错误] 关闭服务失败: {str(e)}")
        finally:
            self.running = False

class BrowserDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("启动浏览器")
        self.setMinimumWidth(400)
        self.init_ui()
        
    def init_ui(self):
        layout = QFormLayout()
        
        # 浏览器类型
        self.browser_type = QComboBox()
        self.browser_type.addItem("正常模式", 0)
        self.browser_type.addItem("无痕模式", 1)
        layout.addRow("浏览器模式:", self.browser_type)
        
        # 无头模式
        self.headless = QComboBox()
        self.headless.addItem("有界面", False)
        self.headless.addItem("无界面", True)
        layout.addRow("界面模式:", self.headless)
        
        # 按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addRow(button_box)
        
        self.setLayout(layout)
        
    def get_options(self):
        return {
            "browser_type": self.browser_type.currentData(),
            "is_headless": self.headless.currentData()
        }

class EnvironmentDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("创建环境")
        self.setMinimumWidth(400)
        self.init_ui()
        
    def init_ui(self):
        layout = QFormLayout()
        
        # 环境名称
        self.name_edit = QLineEdit()
        layout.addRow("环境名称:", self.name_edit)
        
        # 指纹类型
        self.fingerprint_type = QComboBox()
        self.fingerprint_type.addItem("静态指纹", 0)
        self.fingerprint_type.addItem("动态指纹", 1)
        layout.addRow("指纹类型:", self.fingerprint_type)
        
        # 代理类型
        self.proxy_type = QComboBox()
        self.proxy_type.addItem("不使用代理", "不使用代理")
        self.proxy_type.addItem("HTTP代理", "HTTP代理")
        self.proxy_type.addItem("SOCKS5代理", "SOCKS5代理")
        layout.addRow("代理类型:", self.proxy_type)
        
        # 按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addRow(button_box)
        
        self.setLayout(layout)
        
    def get_options(self):
        return {
            "container_name": self.name_edit.text(),
            "as_dynamic_type": self.fingerprint_type.currentData(),
            "proxy_type_name": self.proxy_type.currentData()
        }

class ServiceWidget(QWidget):
    log_signal = pyqtSignal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.client = None
        self.init_ui()
        
        # 刷新定时器
        self.refresh_timer = QTimer(self)
        self.refresh_timer.timeout.connect(self.refresh_status)
        self.refresh_timer.start(5000)  # 每5秒刷新一次
        
    def init_ui(self):
        layout = QVBoxLayout()
        
        # 服务控制区域
        control_group = QGroupBox("服务控制")
        control_layout = QHBoxLayout()
        
        self.status_label = QLabel("状态: 未连接")
        control_layout.addWidget(self.status_label)
        
        self.start_button = QPushButton("启动服务")
        self.start_button.clicked.connect(self.start_service)
        control_layout.addWidget(self.start_button)
        
        self.stop_button = QPushButton("停止服务")
        self.stop_button.clicked.connect(self.stop_service)
        self.stop_button.setEnabled(False)
        control_layout.addWidget(self.stop_button)
        
        control_group.setLayout(control_layout)
        layout.addWidget(control_group)
        
        # 环境列表
        env_group = QGroupBox("环境列表")
        env_layout = QVBoxLayout()
        
        list_control_layout = QHBoxLayout()
        
        self.refresh_button = QPushButton("刷新列表")
        self.refresh_button.clicked.connect(self.refresh_environments)
        list_control_layout.addWidget(self.refresh_button)
        
        self.create_button = QPushButton("创建环境")
        self.create_button.clicked.connect(self.create_environment)
        list_control_layout.addWidget(self.create_button)
        
        env_layout.addLayout(list_control_layout)
        
        self.env_list = QListWidget()
        self.env_list.itemClicked.connect(self.on_environment_clicked)
        env_layout.addWidget(self.env_list)
        
        # 环境操作按钮
        env_actions_layout = QHBoxLayout()
        
        self.start_browser_button = QPushButton("启动浏览器")
        self.start_browser_button.clicked.connect(self.start_browser)
        self.start_browser_button.setEnabled(False)
        env_actions_layout.addWidget(self.start_browser_button)
        
        self.stop_browser_button = QPushButton("停止浏览器")
        self.stop_browser_button.clicked.connect(self.stop_browser)
        self.stop_browser_button.setEnabled(False)
        env_actions_layout.addWidget(self.stop_browser_button)
        
        self.delete_button = QPushButton("删除环境")
        self.delete_button.clicked.connect(self.delete_environment)
        self.delete_button.setEnabled(False)
        env_actions_layout.addWidget(self.delete_button)
        
        env_layout.addLayout(env_actions_layout)
        
        env_group.setLayout(env_layout)
        layout.addWidget(env_group)
        
        self.setLayout(layout)
        
    def log(self, message):
        self.log_signal.emit(message)
        
    def start_service(self):
        try:
            self.log("正在启动HubStudio服务...")
            self.client = HubStudioClient(config_file="config.json")
            
            self.status_label.setText("状态: 已连接")
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            
            self.refresh_environments()
            self.log("HubStudio服务已启动")
        except Exception as e:
            self.log(f"启动服务失败: {str(e)}")
            self.status_label.setText(f"状态: 错误 ({str(e)})")
            
    def stop_service(self):
        if self.client:
            try:
                self.log("正在停止HubStudio服务...")
                self.client.stop()
                self.client = None
                
                self.env_list.clear()
                self.status_label.setText("状态: 未连接")
                self.start_button.setEnabled(True)
                self.stop_button.setEnabled(False)
                self.start_browser_button.setEnabled(False)
                self.stop_browser_button.setEnabled(False)
                self.delete_button.setEnabled(False)
                
                self.log("HubStudio服务已停止")
            except Exception as e:
                self.log(f"停止服务失败: {str(e)}")
                
    def refresh_status(self):
        if not self.client:
            return
            
        try:
            if self.client.is_running():
                self.status_label.setText("状态: 已连接")
            else:
                self.status_label.setText("状态: 未连接")
                self.client = None
                self.start_button.setEnabled(True)
                self.stop_button.setEnabled(False)
        except Exception:
            self.status_label.setText("状态: 连接错误")
            self.client = None
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)
            
    def refresh_environments(self):
        if not self.client:
            return
            
        try:
            self.log("正在获取环境列表...")
            self.env_list.clear()
            
            environments = self.client.get_environments()
            for env in environments:
                item = QListWidgetItem(f"{env.name} (ID: {env.code})")
                item.setData(100, env.code)  # 存储环境ID
                self.env_list.addItem(item)
                
            self.log(f"获取到 {len(environments)} 个环境")
        except Exception as e:
            self.log(f"获取环境列表失败: {str(e)}")
            
    def on_environment_clicked(self, item):
        self.start_browser_button.setEnabled(True)
        self.delete_button.setEnabled(True)
        
        # 检查浏览器状态
        env_id = item.data(100)
        try:
            if not self.client:
                return
                
            browser_status = self.client.get_browser_status(env_id)
            if browser_status and browser_status.is_running:
                self.stop_browser_button.setEnabled(True)
            else:
                self.stop_browser_button.setEnabled(False)
        except Exception:
            self.stop_browser_button.setEnabled(False)
            
    def create_environment(self):
        if not self.client:
            if not os.path.exists('config.json'):
                self.log("配置文件不存在，请先配置应用信息")
                return
                
            self.start_service()
            if not self.client:
                return
                
        dialog = EnvironmentDialog(self)
        if dialog.exec_():
            options = dialog.get_options()
            try:
                self.log(f"正在创建环境: {options['container_name']}...")
                env = self.client.create_environment(**options)
                self.log(f"环境创建成功: {env.name} (ID: {env.code})")
                self.refresh_environments()
            except Exception as e:
                self.log(f"创建环境失败: {str(e)}")
                
    def delete_environment(self):
        if not self.client:
            return
            
        selected_items = self.env_list.selectedItems()
        if not selected_items:
            return
            
        item = selected_items[0]
        env_id = item.data(100)
        env_name = item.text()
        
        try:
            self.log(f"正在删除环境: {env_name}...")
            self.client.delete_environment(env_id)
            self.log(f"环境删除成功: {env_name}")
            self.refresh_environments()
            
            self.start_browser_button.setEnabled(False)
            self.stop_browser_button.setEnabled(False)
            self.delete_button.setEnabled(False)
        except Exception as e:
            self.log(f"删除环境失败: {str(e)}")
            
    def start_browser(self):
        if not self.client:
            return
            
        selected_items = self.env_list.selectedItems()
        if not selected_items:
            return
            
        item = selected_items[0]
        env_id = item.data(100)
        env_name = item.text()
        
        dialog = BrowserDialog(self)
        if dialog.exec_():
            options = dialog.get_options()
            try:
                self.log(f"正在启动浏览器: {env_name}...")
                result = self.client.start_browser(
                    container_code=env_id,
                    browser_type=options["browser_type"],
                    is_headless=options["is_headless"]
                )
                
                if result and result.get("data", {}).get("statusCode", -1) == 0:
                    self.log(f"浏览器启动成功: {env_name}")
                    self.stop_browser_button.setEnabled(True)
                else:
                    self.log(f"浏览器启动失败: {result}")
            except Exception as e:
                self.log(f"启动浏览器失败: {str(e)}")
                
    def stop_browser(self):
        if not self.client:
            return
            
        selected_items = self.env_list.selectedItems()
        if not selected_items:
            return
            
        item = selected_items[0]
        env_id = item.data(100)
        env_name = item.text()
        
        try:
            self.log(f"正在停止浏览器: {env_name}...")
            result = self.client.stop_browser(env_id)
            
            if result and result.get("code", -1) == 0:
                self.log(f"浏览器停止成功: {env_name}")
                self.stop_browser_button.setEnabled(False)
            else:
                self.log(f"浏览器停止失败: {result}")
        except Exception as e:
            self.log(f"停止浏览器失败: {str(e)}")

    def closeEvent(self, event):
        """窗口关闭时的处理"""
        try:
            self.refresh_timer.stop()
            if self.client and self.client.is_running():
                self.stop_service()
        except Exception as e:
            print(f"关闭窗口时出错: {str(e)}")
        finally:
            event.accept() 