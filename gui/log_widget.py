import os
import json
import time
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QTextEdit, QPushButton, QHBoxLayout, QFileDialog, QLabel
from PyQt5.QtCore import QTimer, Qt

class LogWidget(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.log_file = None
        self.load_log_file()
        self.init_ui()
        
        # 设置自动刷新
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.refresh_log)
        self.timer.start(1000)  # 每秒刷新一次
        
    def init_ui(self):
        layout = QVBoxLayout()
        
        # 日志显示
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        self.log_path_label = QLabel("日志文件: " + (self.log_file or "未加载"))
        button_layout.addWidget(self.log_path_label, 1)
        
        clear_button = QPushButton("清空")
        clear_button.clicked.connect(self.clear_log)
        button_layout.addWidget(clear_button)
        
        browse_button = QPushButton("浏览...")
        browse_button.clicked.connect(self.browse_log_file)
        button_layout.addWidget(browse_button)
        
        refresh_button = QPushButton("刷新")
        refresh_button.clicked.connect(self.refresh_log)
        button_layout.addWidget(refresh_button)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
        
        # 初始加载日志
        self.refresh_log()
        
    def load_log_file(self):
        # 尝试从配置文件加载日志路径
        if os.path.exists('config.json'):
            with open('config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
                log_file = config.get('log_file')
                if log_file and os.path.exists(log_file):
                    self.log_file = log_file
                    return
                    
        # 如果配置中没有指定或文件不存在，使用默认路径
        default_log = 'logs/hubstudio.log'
        if os.path.exists(default_log):
            self.log_file = default_log
        
    def refresh_log(self):
        if not self.log_file or not os.path.exists(self.log_file):
            return
            
        try:
            with open(self.log_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 只显示最后1000行，避免过多内容导致性能问题
            lines = content.split('\n')
            if len(lines) > 1000:
                content = '\n'.join(lines[-1000:])
                
            self.log_text.setText(content)
            
            # 滚动到底部
            cursor = self.log_text.textCursor()
            cursor.movePosition(cursor.End)
            self.log_text.setTextCursor(cursor)
        except Exception as e:
            self.log_text.setText(f"读取日志文件错误: {str(e)}")
        
    def clear_log(self):
        self.log_text.clear()
        
    def browse_log_file(self):
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择日志文件", "", "日志文件 (*.log)"
        )
        if file_path:
            self.log_file = file_path
            self.log_path_label.setText("日志文件: " + self.log_file)
            self.refresh_log() 