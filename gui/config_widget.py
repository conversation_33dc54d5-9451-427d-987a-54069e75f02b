import json
import os
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QFormLayout, 
                           QLineEdit, QSpinBox, QCheckBox, 
                           QPushButton, QMessageBox, QFileDialog)
from PyQt6.QtCore import Qt

class ConfigWidget(QWidget):
    def __init__(self):
        super().__init__()
        self.config_file = "config.json"
        self.init_ui()
        self.load_config()
        
    def init_ui(self):
        layout = QVBoxLayout()
        form_layout = QFormLayout()
        
        # 创建配置项输入框
        self.host = QLineEdit()
        self.port = QSpinBox()
        self.port.setRange(1, 65535)
        self.app_id = QLineEdit()
        self.app_secret = QLineEdit()
        self.group_code = QLineEdit()
        self.client_path = QLineEdit()
        self.browse_btn = QPushButton("浏览...")
        self.browse_btn.clicked.connect(self.browse_client_path)
        
        self.log_level = QLineEdit()
        self.log_file = QLineEdit()
        self.auto_start = QCheckBox()
        
        # 添加到表单布局
        form_layout.addRow("主机:", self.host)
        form_layout.addRow("端口:", self.port)
        form_layout.addRow("App ID:", self.app_id)
        form_layout.addRow("App Secret:", self.app_secret)
        form_layout.addRow("Group Code:", self.group_code)
        form_layout.addRow("客户端路径:", self.client_path)
        form_layout.addRow("", self.browse_btn)
        form_layout.addRow("日志级别:", self.log_level)
        form_layout.addRow("日志文件:", self.log_file)
        form_layout.addRow("自动启动:", self.auto_start)
        
        # 保存按钮
        save_btn = QPushButton("保存配置")
        save_btn.clicked.connect(self.save_config)
        
        layout.addLayout(form_layout)
        layout.addWidget(save_btn)
        self.setLayout(layout)
    
    def browse_client_path(self):
        path = QFileDialog.getExistingDirectory(self, "选择HubStudio客户端目录")
        if path:
            self.client_path.setText(path)
    
    def load_config(self):
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    
                self.host.setText(config.get('host', 'localhost'))
                self.port.setValue(config.get('port', 6873))
                self.app_id.setText(config.get('app_id', ''))
                self.app_secret.setText(config.get('app_secret', ''))
                self.group_code.setText(config.get('group_code', ''))
                self.client_path.setText(config.get('client_path', ''))
                self.log_level.setText(config.get('log_level', 'INFO'))
                self.log_file.setText(config.get('log_file', 'logs/hubstudio.log'))
                self.auto_start.setChecked(config.get('auto_start', True))
        except Exception as e:
            QMessageBox.warning(self, "错误", f"加载配置文件失败: {str(e)}")
    
    def save_config(self):
        try:
            config = {
                'host': self.host.text(),
                'port': self.port.value(),
                'app_id': self.app_id.text(),
                'app_secret': self.app_secret.text(),
                'group_code': self.group_code.text(),
                'client_path': self.client_path.text(),
                'log_level': self.log_level.text(),
                'log_file': self.log_file.text(),
                'auto_start': self.auto_start.isChecked(),
                'connector': {
                    'server_mode': 'http',
                    'threads': 10,
                    'timeout': 60,
                    'line_setting': False,
                    'remote_debugging': False,
                    'heartbeat_timeout': 60,
                    'open_wait_sec': 60
                }
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4)
            
            QMessageBox.information(self, "成功", "配置已保存")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"保存配置文件失败: {str(e)}") 