import sys
import os
from PyQt6.QtWidgets import (QApplication, QMainWindow, QTabWidget,
                           QVBoxLayout, QWidget)
from PyQt6.QtGui import QIcon
from config_widget import ConfigWidget
from service_widget import ServiceWidget
from log_widget import LogWidget

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("HubStudio 管理器")
        self.setMinimumSize(800, 600)
        
        # 创建主窗口部件
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        
        # 创建布局
        layout = QVBoxLayout()
        main_widget.setLayout(layout)
        
        # 创建标签页
        tabs = QTabWidget()
        tabs.addTab(ConfigWidget(), "配置管理")
        tabs.addTab(ServiceWidget(), "服务控制")
        tabs.addTab(LogWidget(), "日志查看")
        
        layout.addWidget(tabs)

def main():
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main() 