"""
HubStudio SDK 同步使用示例
"""
import os
import json
import time
import random
import string
from datetime import datetime
from typing import List, Dict, Optional
from hubstudio_sdk import (
    HubStudioClient,
    HubStudioError,
    Environment,
    BrowserStatus,
)
from logs import get_logger
from config import load_config, get_default_config_path
from server.proxy_server import MultiProxyManager, ProxyServer
from utility import IPManager

# 创建logger实例
logger = get_logger("hubstudio_sync")

def handle_browser(container_code: int, client: HubStudioClient):
    """处理浏览器自动化任务"""
    try:
        # 使用start_playwright替代open_browser
        browser_info, browser = client.start_playwright(container_code)
        # 不需要使用with语句，直接使用返回的browser对象
        page = browser.contexts[0].pages[0]
        page.goto("https://www.ozon.ru")
        page.wait_for_load_state("networkidle")
        
        # 截图
        page.pause()
        now = datetime.now().strftime("%Y%m%d%H%M%S")
        filename = f"screenshot_{now}.png"
        page.screenshot(path=filename)
        
        time.sleep(3)
        
        # 关闭页面
        page.close()
        # 关闭browser
        browser.close()
    except Exception as e:
        print(f"操作浏览器时发生错误: {str(e)}")

def main():
    # # 初始化多网卡代理管理器
    # proxy_manager = MultiProxyManager(base_port=1080)
    # num_proxies = proxy_manager.start_all()
    # logger.info(f"启动了 {num_proxies} 个代理服务")
    #
    # # 打印代理信息
    # for info in proxy_manager.get_proxy_info():
    #     logger.info(f"代理: {info['interface']} -> {info['ip']}:{info['port']}")
    proxy = ProxyServer("WLAN2", 1081)
    proxy.start()
    # 获取默认配置文件路径
    config_path = get_default_config_path()
    # 加载配置文件
    config = load_config(config_path)
    # 使用配置文件方式初始化客户端
    client = HubStudioClient(config=config)
    # 使用加载的配置初始化IP管理器
    ip_manager = IPManager(config=config)
    
    try:
        buey='79104684025'
        # 获取环境列表
        envs = client.get_environments(container_name=buey)
        if not envs:
            print("没有可用的环境")
            return
        # 选择第一个环境
        env = None
        for env in envs:
            if env.container_name == buey:
                env = env
                ip_manager.change(buey)
                print(f"选择环境: {env.container_name} (ID: {env.container_code})")
                break
        if not env:
            print("没有找到指定环境")
            return
        # 打开浏览器并处理任务
        handle_browser(env.container_code, client)
        
    except HubStudioError as e:
        print(f"发生错误: {e}")
    finally:
        # 关闭客户端连接
        client.stop()
        # 停止所有代理服务
        # proxy_manager.stop_all()
        proxy.stop()

if __name__ == "__main__":
    logger.info("程序开始运行")
    # 单网卡使用
    # proxy = ProxyServer("WLAN2", 1080)
    # proxy.start()
    main()
