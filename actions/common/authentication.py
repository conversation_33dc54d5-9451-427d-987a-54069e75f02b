"""
认证相关抽象基类
"""
from abc import abstractmethod
from ..base.action import Action
from typing import Dict, Optional

class BaseLogin(Action):
    """登录动作抽象基类"""
    
    @abstractmethod
    async def execute(self, page, username: str, password: str, **kwargs) -> bool:
        """
        执行登录操作的抽象方法
        
        Args:
            page: Playwright页面对象
            username: 用户名
            password: 密码
            **kwargs: 其他平台特定参数
            
        Returns:
            bool: 登录是否成功
        """
        pass
    
    async def handle_captcha(self, page) -> bool:
        """
        处理可能出现的验证码，子类可重写
        
        Args:
            page: Playwright页面对象
            
        Returns:
            bool: 验证码处理是否成功
        """
        self.logger.info("基类验证码处理: 无验证码或不需要处理")
        return True  # 默认实现，假设没有验证码 