"""
购物相关抽象基类
"""
from abc import abstractmethod
from ..base.action import Action
from typing import Optional, Dict, Any

class BaseAddToCart(Action):
    """添加购物车抽象基类"""
    
    @abstractmethod
    async def execute(self, page, product_url: Optional[str] = None, 
                    product_id: Optional[str] = None, **kwargs) -> bool:
        """
        添加商品到购物车的抽象方法
        
        Args:
            page: Playwright页面对象
            product_url: 商品URL
            product_id: 商品ID
            **kwargs: 其他平台特定参数
            
        Returns:
            bool: 操作是否成功
        """
        pass

class BasePurchase(Action):
    """完成购买抽象基类"""
    
    @abstractmethod
    async def execute(self, page, **kwargs) -> bool:
        """
        执行购买流程的抽象方法
        
        Args:
            page: Playwright页面对象
            **kwargs: 平台特定参数
            
        Returns:
            bool: 购买是否成功
        """
        pass 