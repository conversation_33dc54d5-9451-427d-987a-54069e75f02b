"""
动作基类定义
"""
from abc import ABC, abstractmethod
from typing import Any, Dict, Optional
import logging

class Action(ABC):
    """所有动作的基类"""
    
    def __init__(self, context=None, config=None):
        """
        初始化动作
        
        Args:
            context: 上下文对象，通常是HubStudioClient实例
            config: 配置对象
        """
        self.context = context
        self.config = config or {}
        self.logger = self._setup_logger()
    
    def _setup_logger(self):
        """设置日志记录器"""
        logger_name = f"action.{self.__class__.__name__}"
        logger = logging.getLogger(logger_name)
        return logger
    
    @abstractmethod
    async def execute(self, *args, **kwargs) -> Any:
        """
        执行动作的抽象方法
        
        Args:
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            Any: 动作执行结果
        """
        pass
    
    async def validate(self, *args, **kwargs) -> bool:
        """
        验证动作参数是否有效
        
        Args:
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            bool: 参数是否有效
        """
        return True
    
    async def with_retry(self, max_retries=3, delay=1):
        """
        重试机制装饰器
        
        Args:
            max_retries: 最大重试次数
            delay: 重试延迟（秒）
        """
        # 实现重试逻辑
        pass 