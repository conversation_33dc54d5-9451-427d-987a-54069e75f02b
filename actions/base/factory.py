"""
动作工厂类定义
"""
from typing import Dict, Type, Any
from .action import Action

class ActionRegistry:
    """动作注册中心"""
    
    _registry = {}
    
    @classmethod
    def register(cls, action_name: str, action_class: Type[Action]):
        """
        注册动作类
        
        Args:
            action_name: 动作名称
            action_class: 动作类
        """
        cls._registry[action_name] = action_class
        
    @classmethod
    def create(cls, action_name: str, context=None, config=None) -> Action:
        """
        创建动作实例
        
        Args:
            action_name: 动作名称
            context: 上下文对象
            config: 配置对象
            
        Returns:
            Action: 动作实例
        """
        if action_name not in cls._registry:
            raise ValueError(f"未注册的动作: {action_name}")
        
        action_class = cls._registry[action_name]
        return action_class(context=context, config=config)
    
    @classmethod
    def list_actions(cls) -> Dict[str, Type[Action]]:
        """
        列出所有注册的动作
        
        Returns:
            Dict[str, Type[Action]]: 动作名称到动作类的映射
        """
        return cls._registry.copy()

class PlatformActionFactory:
    """平台特定动作工厂"""
    
    _platforms = {
        'wildberries': {},
        'ozon': {},
        'yandex_market': {}
    }
    
    @classmethod
    def register(cls, platform: str, action_type: str, action_class: Type[Action]):
        """
        注册平台特定动作
        
        Args:
            platform: 平台名称
            action_type: 动作类型
            action_class: 动作类
        """
        if platform not in cls._platforms:
            cls._platforms[platform] = {}
        cls._platforms[platform][action_type] = action_class
    
    @classmethod
    def create(cls, platform: str, action_type: str, context=None, config=None) -> Action:
        """
        创建特定平台的动作实例
        
        Args:
            platform: 平台名称('wildberries', 'ozon', 'yandex_market')
            action_type: 动作类型('login', 'add_to_cart'等)
            context: 上下文对象
            config: 配置数据
            
        Returns:
            Action: 平台特定的动作实例
        """
        if platform not in cls._platforms:
            raise ValueError(f"不支持的平台: {platform}")
            
        platform_actions = cls._platforms[platform]
        if action_type not in platform_actions:
            raise ValueError(f"平台{platform}不支持动作: {action_type}")
            
        action_class = platform_actions[action_type]
        return action_class(context=context, config=config) 