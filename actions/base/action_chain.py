"""
动作链基类定义
"""
from typing import List, Dict, Any
from .action import Action
import logging

class ActionChain:
    """动作链，用于组合多个动作"""
    
    def __init__(self, context=None, config=None):
        """
        初始化动作链
        
        Args:
            context: 上下文对象
            config: 配置对象
        """
        self.actions = []
        self.context = context
        self.config = config or {}
        self.logger = logging.getLogger(f"action_chain.{self.__class__.__name__}")
        
    def add(self, action: Action):
        """
        添加动作到链中
        
        Args:
            action: 动作实例
            
        Returns:
            ActionChain: 返回自身，支持链式调用
        """
        self.actions.append(action)
        return self
        
    async def execute_all(self, *args, **kwargs) -> List[Any]:
        """
        执行所有动作并返回结果列表
        
        Args:
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            List[Any]: 每个动作的执行结果
        """
        results = []
        for i, action in enumerate(self.actions):
            self.logger.info(f"执行动作链中的第{i+1}个动作: {action.__class__.__name__}")
            try:
                result = await action.execute(*args, **kwargs)
                results.append(result)
                # 如果某个动作失败，可以选择中断链
                if result is False and self.config.get('break_on_failure', False):
                    self.logger.warning(f"动作 {action.__class__.__name__} 执行失败，中断链")
                    break
            except Exception as e:
                self.logger.error(f"动作 {action.__class__.__name__} 执行异常: {str(e)}")
                results.append(None)
                if self.config.get('break_on_failure', False):
                    break
        return results 