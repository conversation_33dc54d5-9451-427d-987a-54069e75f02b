# HubStudio SDK

HubStudio SDK 是一个用于操作 HubStudio 浏览器环境的 Python SDK。它提供了完整的 API 封装，支持环境管理、浏览器控制、分组管理等功能。

## 安装

```bash
pip install hubstudio-sdk
```

## 快速开始

### 基本使用

```python
from hubstudio_sdk import HubStudioClient

# 创建客户端
client = HubStudioClient(
    app_id="your_app_id",
    app_secret="your_app_secret",
    group_code="your_group_code"
)

# 使用上下文管理器（推荐）
with HubStudioClient(
    app_id="your_app_id",
    app_secret="your_app_secret",
    group_code="your_group_code"
) as client:
    # 登录
    client.login()
    
    # 获取环境列表
    environments = client.get_environments()
    for env in environments:
        print(f"环境: {env.container_name}")
    
    # 创建新环境
    env = client.create_environment(
        container_name="测试环境",
        as_dynamic_type=1,
        proxy_type_name="不使用代理"
    )
    
    # 启动浏览器
    browser = client.start_browser(
        container_code=env.container_code,
        is_headless=False
    )
    
    # 关闭浏览器
    client.stop_browser(env.container_code)
```

### 配置管理

SDK 支持多种配置方式，按优先级从高到低：

1. 初始化参数
2. 环境变量
3. 配置文件
4. 默认值

#### 使用配置文件

创建 `config.json`:
```json
{
    "host": "localhost",
    "port": 6873,
    "app_id": "your_app_id",
    "app_secret": "your_app_secret",
    "group_code": "your_group_code",
    "client_path": "path/to/hubstudio",
    "log_level": "INFO",
    "log_file": "logs/hubstudio.log",
    "auto_start": true,
    "connector": {
        "server_mode": "http",
        "threads": 10,
        "timeout": 60,
        "line_setting": false,
        "remote_debugging": false,
        "heartbeat_timeout": 60,
        "open_wait_sec": 60
    }
}
```

```python
client = HubStudioClient(config_file="config.json")
```

#### 使用环境变量

```bash
# 基础配置
export HUBSTUDIO_APP_ID=your_app_id
export HUBSTUDIO_APP_SECRET=your_app_secret
export HUBSTUDIO_GROUP_CODE=your_group_code
export HUBSTUDIO_LOG_LEVEL=DEBUG

# Connector 配置
export HUBSTUDIO_CONNECTOR_THREADS=20
export HUBSTUDIO_CONNECTOR_TIMEOUT=120
```

### 日志管理

SDK 支持同时输出到控制台和文件：

```python
# 仅控制台输出
client = HubStudioClient(log_level="DEBUG")

# 同时输出到文件
client = HubStudioClient(
    log_level="DEBUG",
    log_file="logs/hubstudio.log"
)
```

## 功能特性

### 环境管理
- 创建环境
- 更新环境
- 删除环境
- 获取环境列表
- 刷新指纹

### 浏览器控制
- 启动浏览器
- 关闭浏览器
- 获取浏览器状态
- 窗口排列
- 清理缓存
- 重置插件

### 分组管理
- 创建分组
- 删除分组
- 获取分组列表

### Cookie 管理
- 导入 Cookie
- 导出 Cookie

### 高级指纹设置
- 自定义 UA
- 地理位置
- WebRTC
- Canvas
- WebGL
- 等更多设置

### 系统功能
- 配置管理
- 日志记录
- 自动启停
- 状态监控

## 错误处理

SDK 使用 `HubStudioError` 异常类处理所有错误：

```python
from hubstudio_sdk import HubStudioClient, HubStudioError

try:
    client = HubStudioClient()
    client.login()
except HubStudioError as e:
    print(f"错误代码: {e.code}")
    print(f"错误信息: {str(e)}")
```

## 文档

更多详细信息请参考：
- [HubStudio 官方文档](https://www.hubstudio.cn)
- [SDK API 文档](https://docs.hubstudio.cn/sdk) 