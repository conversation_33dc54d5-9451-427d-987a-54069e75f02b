"""IP管理模块，用于管理和切换IP地址。"""
# 标准库导入
import json
import os
import socket
import time
from datetime import datetime, timedelta

# 第三方库导入
import psutil
import pywifi
import requests
from ping3 import ping
from requests_toolbelt.adapters import source

# 本地应用导入
from logs import get_logger
from lendian_sdk.api import LDApi
from server.proxy_server import ProxyServer

# 创建日志记录器
logger = get_logger("ip_manager")


def load_config(config_path="config.json"):
    """
    加载配置文件。
    
    Args:
        config_path (str): 配置文件路径，默认为'config.json'
        
    Returns:
        dict: 配置对象，如果加载失败则返回空字典
    """
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        logger.info(f"成功加载配置文件: {config_path}")
        return config
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}")
        return {}


class IPManager:
    """
    IP管理器，用于管理动态IP的切换和跟踪。
    
    此类使用内存缓存，管理已使用过的IP地址和客户端标识，
    通过控制设备飞行模式和WiFi连接实现IP地址的切换。
    
    缓存系统会自动处理过期的IP记录，默认过期时间为3600秒（1小时）。
    """
    
    def __init__(self, config=None, adb=None):
        """
        初始化IP管理器。
        
        Args:
            config (dict): 配置对象，默认为None，会使用空字典
            adb (LDApi): 雷电模拟器ADB接口，如果为None则从配置文件读取
        """
        # 使用传入的配置或空字典
        self.config = config or {}
        
        # 设置内存缓存
        self.cache = {}  # 简单的内存缓存
        self.last_client = None
        # 从配置文件读取IP过期时间
        self.ip_expire_time = self.config.get('ip_manager', {}).get('expire_time', 3600)  # 默认1小时

        # 设置雷电模拟器
        if adb is None:
            lei_dian_path = self.config.get('lei_dian', {}).get('path', r'D:\leidian\LDPlayer9')
            if lei_dian_path == "":
                lei_dian_path = r'D:\leidian\LDPlayer9'  # 默认路径
            self.adbc = LDApi(lei_dian_path).adb
        else:
            self.adbc = adb
            
        # 从配置文件读取网卡信息
        wifi_config = self.config.get('ip_manager', {}).get('wifi', {})
        self.iface_usb_name = wifi_config.get('usb', {
            'iface_name': 'Realtek 8811CU Wireless LAN 802.11ac USB NIC', 
            'wifi': 'WI-FI'
        })
        
        self.iface_pci_name = wifi_config.get('pci', {
            'iface_name': 'Realtek 8821CE Wireless LAN 802.11ac PCI-E NIC', 
            'wifi': 'WLAN2'
        })
        
        # 从配置文件读取设备信息
        device_config = self.config.get('ip_manager', {}).get('device', {})
        self.device = device_config.get('id', 'Q7PRX18C10003990')
        self.wait_time = device_config.get('wait_time', 40)
        
        # 从配置文件读取WiFi连接设置
        wifi_connection = self.config.get('ip_manager', {}).get('wifi_connection', {})
        self.interfaces = pywifi.PyWiFi().interfaces()
        self.profile = pywifi.Profile()
        self.profile.ssid = wifi_connection.get('ssid', 'Honor 10 Lite')
        
        logger.info(f"IP管理器初始化完成，设备ID: {self.device}, 等待时间: {self.wait_time}秒")
    
    def change(self, client_id):
        """
        触发IP切换事件。
        
        Args:
            client_id (str): 客户端标识
            
        Returns:
            tuple: (设备ID, ADB接口)
        """
        # 触发换IP事件
        if self.last_client == client_id:
            logger.info('无需切换IP')
            return self.device, self.adbc
        self._switch_wifi()
        self.last_client = client_id
        return self.device, self.adbc
    
    def _ip_exists(self, ip):
        """
        检查IP是否在缓存中且未过期。
        
        Args:
            ip (str): 要检查的IP地址
            
        Returns:
            bool: IP存在且未过期返回True，否则返回False
        """
        if ip in self.cache:
            expire_time = self.cache[ip]['expire_time']
            if datetime.now() < expire_time:
                return True
            else:
                # 删除过期的缓存项
                del self.cache[ip]
        return False
    
    def _cache_ip(self, ip):
        """
        将IP添加到缓存中。
        
        Args:
            ip (str): 要缓存的IP地址
        """
        expire_time = datetime.now() + timedelta(seconds=self.ip_expire_time)
        self.cache[ip] = {
            'time': datetime.now(),
            'expire_time': expire_time
        }
    
    def delete_ip(self, ip):
        """
        从缓存中删除指定IP。
        
        Args:
            ip (str): 要删除的IP地址
            
        Returns:
            bool: 删除成功返回True，IP不存在返回False
        """
        if ip in self.cache:
            del self.cache[ip]
            logger.info(f'已删除IP: {ip}')
            return True
        logger.info(f'IP不存在: {ip}')
        return False
    
    def get_all_ips(self):
        """
        获取所有未过期的IP及其信息。
        
        Returns:
            dict: IP地址作为键，值为包含时间信息的字典
        """
        # 清理过期IP
        self._clean_expired_ips()
        
        # 返回所有有效IP
        return self.cache
    
    def get_current_ip(self):
        """
        获取当前网络的IP地址。
        
        Returns:
            str: 当前IP地址
        """
        try:
            return self._get_ip(self.iface_pci_name['wifi'])
        except Exception as e:
            logger.error(f'获取当前IP失败: {e}')
            return None
    
    def _clean_expired_ips(self):
        """清理所有过期的IP缓存。"""
        now = datetime.now()
        expired_ips = [ip for ip, data in self.cache.items() 
                      if data['expire_time'] < now]
        
        for ip in expired_ips:
            del self.cache[ip]
            logger.debug(f'清理过期IP: {ip}')
        
        if expired_ips:
            logger.info(f'已清理 {len(expired_ips)} 个过期IP')

    def _switch_wifi(self):
        """
        切换WiFi获取新IP地址。
        
        Returns:
            bool: 切换成功返回True
        """
        logger.info('开始操作真机切换WIFI')
        self.adbc.swipe(self.device, 890, 36, 890, 467, 500)  # 下拉
        while True:
            if self.adbc.get_airplane_mode_state(self.device) == '1':  # 判断是否是开启飞行模式
                self.adbc.click(self.device, 194, 1218)  # 点击关闭飞行模式
                time.sleep(0.3)
                self.adbc.click(self.device, 194, 1510)  # 点击开启热点
                logger.info('真机已关闭飞行模式并打开热点')
                logger.info('操作连接真机WIFI')
                if self._link_wifi():
                    for i in range(15):  # 设置重试次数
                        try:
                            logger.info(f'测试是否连通网络，等待{i+1}秒')
                            time.sleep(i+1)
                            new_ip = self._get_ip(self.iface_pci_name['wifi'])
                            break  # 成功运行后退出循环
                        except Exception as error:
                            if i != 0:
                                print(f"Run failed, retrying. Error: {error}")
                        if i < 15:  # 如果还有剩余尝试次数，继续尝试
                            continue
                        else:
                            raise error  # 超过最大重试次数，抛出异常
                    logger.info(f'真机当前IP:{new_ip}')
                    if self._ip_exists(new_ip):  # IP相同
                        self.adbc.click(self.device, 194, 1218)  # 点击开启飞行模式
                        logger.debug(f'IP未变化请等待{40}秒再次切换')
                        time.sleep(40)
                    else:
                        self._cache_ip(new_ip)
                        logger.info(f'成功切换IP:{new_ip}')
                        self.adbc.input_key(self.device, 'BACK')
                        return True
            else:  # 未开启飞行模式
                self.adbc.click(self.device, 194, 1218)  # 点击开启飞行模式
                logger.info('真机已开启飞行模式')
                logger.debug(f'请等待{self.wait_time}秒后切换')
                time.sleep(self.wait_time)

    def _get_ip(self, wifi_name):
        """
        获取指定网卡的公网IP地址。
        
        Args:
            wifi_name (str): 网卡名称
            
        Returns:
            str: 公网IP地址
        """
        net_if_addrs = psutil.net_if_addrs()  # 获取本地网卡信息
        iface_ip = None
        if wifi_name in net_if_addrs:
            # 获取该网卡的所有IPv4
            for iface in net_if_addrs[wifi_name]:
                if iface.family == socket.AF_INET:
                    iface_ip = iface.address
        if iface_ip is not None:
            s = requests.session()
            s.headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 '
                              '(KHTML, like Gecko) Chrome/********* Safari/537.36',
            }
            new_s = source.SourceAddressAdapter(iface_ip)
            s.mount('http://', new_s)
            s.mount('https://', new_s)
            return s.get('https://www.svetx.com/ip').text
        return None

    def _link_wifi(self):
        """
        连接WiFi热点。
        
        Returns:
            bool: 连接成功返回True，否则返回False
        """
        iface = None
        for temp in self.interfaces:
            if temp.name() == self.iface_pci_name['iface_name']:
                iface = temp
        if iface is None:
            logger.info('找不到USB网卡')
            return False
        if iface.status() == 4:
            return True

        iface.connect(self.profile)  # 通过添加的Profile 连接指定wifi
        time.sleep(10)  # 休眠10s
        while True:
            if iface.status() == 4:
                return True
            else:
                logger.info('无法链接手机热点，重新检测5秒')
                time.sleep(5)  # 休眠5s


if __name__ == '__main__':
    # 加载配置文件
    config = load_config()
    
    # 使用加载的配置初始化IP管理器
    ip_manager = IPManager(config=config)
    
    # 获取当前IP
    current_ip = ip_manager.get_current_ip()
    print(f"当前IP: {current_ip}")
    
    # 使用IP管理器切换IP
    ip_manager.change('79801811276')
    
    # 查看所有缓存的IP
    all_ips = ip_manager.get_all_ips()
    print("\n当前缓存的所有IP:")
    for ip, data in all_ips.items():
        expire_time = data['expire_time'].strftime('%Y-%m-%d %H:%M:%S')
        print(f"  - {ip} (过期时间: {expire_time})")
    
    # 演示删除IP
    if current_ip and current_ip in all_ips:
        print(f"\n删除IP: {current_ip}")
        ip_manager.delete_ip(current_ip)
        
        # 验证删除后的结果
        updated_ips = ip_manager.get_all_ips()
        print("\n删除后缓存的IP:")
        for ip, data in updated_ips.items():
            expire_time = data['expire_time'].strftime('%Y-%m-%d %H:%M:%S')
            print(f"  - {ip} (过期时间: {expire_time})")