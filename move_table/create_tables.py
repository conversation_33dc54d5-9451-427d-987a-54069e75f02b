import asyncio
import json
from models.buyer_info import db, BuyerInfo

async def add_column_comments():
    comments = {
        'id': '主键ID',
        'birthday': '生日',
        'card_body': '银行卡号主体',
        'card_csv': '银行卡CSV安全码',
        'card_date': '银行卡有效期',
        'card_num': '完整银行卡号',
        'date_issue': '证件签发日期',
        'device_id': '设备ID',
        'email': '电子邮箱',
        'email_password': '邮箱密码',
        'email_url': '邮箱登录URL',
        'fms_code': 'FMS代码',
        'fms_name': 'FMS名称',
        'is_done': '是否完成',
        'name': '姓名',
        'parent_name': '父名',
        'passport_number': '护照号码',
        'passport_url': '护照图片URL',
        'phone': '手机号码',
        'port': '端口号',
        'proxy_cn': '代理服务器CN',
        'proxy_gost': '代理服务器GOST',
        'sex': '性别',
        'surname': '姓氏',
        'is_seller': '是否为卖家',
        'is_ozon': '是否使用Ozon平台',
        'pay_ozon': 'Ozon平台支付方式',
        'is_yandex_market': '是否使用Yandex Market平台',
        'pay_yandex_market': 'Yandex Market平台支付方式',
        'is_wildberries': '是否使用Wildberries平台',
        'pay_wildberries': 'Wildberries平台支付方式',
        'is_mail_ru': '是否使用Mail.ru',
        'is_yoo_money': '是否使用YooMoney',
        'is_telegram': '是否使用Telegram',
        'is_whats_app': '是否使用WhatsApp',
        'is_vk': '是否使用VK',
        'is_sberbank': '是否使用Sberbank',
        'is_vtb_bank': '是否使用VTB Bank',
        'is_tinkoff_bank': '是否使用Tinkoff Bank',
        'created_at': '创建时间',
        'updated_at': '更新时间'
    }
    
    for column, comment in comments.items():
        await db.status(f"""
            COMMENT ON COLUMN review.tb_buyer_info.{column} IS '{comment}'
        """)

async def create_tables():
    # 读取数据库配置
    with open('hubstudio.json', 'r') as f:
        config = json.load(f)
        db_config = config['postgresql']

    # 构建数据库 URL
    db_url = f"postgresql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}"
    
    try:
        print("正在连接数据库...")
        await db.set_bind(db_url)
        
        print("正在创建 review schema...")
        await db.status('CREATE SCHEMA IF NOT EXISTS review')
        
        print("正在创建表...")
        await db.gino.create_all()
        
        print("正在添加字段注释...")
        await add_column_comments()
        
        # 添加表注释
        await db.status("""
            COMMENT ON TABLE review.tb_buyer_info IS '买家信息表'
        """)
        
        print("表和注释创建成功！")
    except Exception as e:
        print(f"创建表时出错: {str(e)}")
    finally:
        if db.bind:
            print("正在关闭数据库连接...")
            await db.pop_bind().close()
            print("数据库连接已关闭")

if __name__ == "__main__":
    asyncio.run(create_tables()) 