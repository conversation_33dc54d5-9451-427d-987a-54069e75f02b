import json
import asyncio
from gino import Gino

# 读取配置文件
with open('hubstudio.json', 'r') as f:
    config = json.load(f)
    db_config = config['postgresql']

# 构建数据库 URL
db_url = f"postgresql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}"
print(f"正在尝试连接到数据库: {db_config['host']}:{db_config['port']}/{db_config['database']}")

# 初始化 GINO
db = Gino()

async def test_connection():
    try:
        print("正在建立数据库连接...")
        # 尝试连接数据库
        await db.set_bind(db_url)
        print("数据库连接成功！")
        
        # 执行简单查询测试
        result = await db.scalar('SELECT 1')
        print(f"测试查询结果: {result}")
        
    except Exception as e:
        print(f"连接失败: {str(e)}")
        print(f"连接URL (密码已隐藏): postgresql://{db_config['user']}:****@{db_config['host']}:{db_config['port']}/{db_config['database']}")
    finally:
        try:
            if db.bind:
                print("正在关闭数据库连接...")
                await db.pop_bind().close()
                print("数据库连接已关闭")
        except Exception as e:
            print(f"关闭连接时出错: {str(e)}")

if __name__ == "__main__":
    asyncio.run(test_connection()) 