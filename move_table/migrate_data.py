import asyncio
import json
from datetime import datetime
from pymongo import MongoClient
from models.buyer_info import db, BuyerInfo
from urllib.parse import quote_plus

async def migrate_data():
    # 读取 PostgreSQL 配置
    with open('hubstudio.json', 'r') as f:
        config = json.load(f)
        db_config = config['postgresql']

    # 构建 PostgreSQL URL
    db_url = f"postgresql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}"
    
    try:
        print("正在连接 PostgreSQL...")
        await db.set_bind(db_url)
        
        # MongoDB 连接信息
        mongo_username = quote_plus("root")
        mongo_password = quote_plus("AirSmile@1688")
        mongo_host = "************"
        mongo_port = "27017"
        
        # 构建 MongoDB 连接 URL
        mongo_url = f"mongodb://{mongo_username}:{mongo_password}@{mongo_host}:{mongo_port}/"
        
        # 连接 MongoDB
        print("正在连接 MongoDB...")
        mongo_client = MongoClient(mongo_url)
        mongo_db = mongo_client['review']
        mongo_collection = mongo_db['buyer_info']
        
        # 测试连接
        try:
            mongo_client.admin.command('ping')
            print("MongoDB 连接成功！")
        except Exception as e:
            raise Exception(f"MongoDB 连接失败: {str(e)}")
        
        # 获取所有 MongoDB 数据
        print("正在读取 MongoDB 数据...")
        mongo_data = list(mongo_collection.find())
        total_records = len(mongo_data)
        print(f"总共找到 {total_records} 条记录")
        
        # 迁移数据
        print("开始迁移数据...")
        success_count = 0
        error_count = 0
        
        for idx, doc in enumerate(mongo_data, 1):
            try:
                # 转换数据格式
                buyer_data = {
                    'birthday': doc.get('birthday'),
                    'card_body': doc.get('cardBody'),
                    'card_csv': doc.get('cardCSV'),
                    'card_date': doc.get('cardDate'),
                    'card_num': doc.get('cardNum'),
                    'date_issue': doc.get('dateIssue'),
                    'device_id': doc.get('device_id'),
                    'email': doc.get('email'),
                    'email_password': doc.get('emailPassword'),
                    'email_url': doc.get('emailUrl'),
                    'fms_code': doc.get('fmsCode'),
                    'fms_name': doc.get('fmsName'),
                    'is_done': doc.get('isDone'),
                    'name': doc.get('name'),
                    'parent_name': doc.get('parentName'),
                    'passport_number': doc.get('passportNumber'),
                    'passport_url': doc.get('passportUrl'),
                    'phone': doc.get('phone'),
                    'port': doc.get('port'),
                    'proxy_cn': doc.get('proxyCn'),
                    'proxy_gost': doc.get('proxyGost'),
                    'sex': doc.get('sex'),
                    'surname': doc.get('surname'),
                    'is_seller': doc.get('isSeller', False),
                    'is_ozon': doc.get('isOzon', False),
                    'pay_ozon': doc.get('payOzon'),
                    'is_yandex_market': doc.get('isYandexMarket', False),
                    'pay_yandex_market': doc.get('payYandexMarket'),
                    'is_wildberries': doc.get('isWildberries', False),
                    'pay_wildberries': doc.get('payWildberries'),
                    'is_mail_ru': doc.get('isMailRu', False),
                    'is_yoo_money': doc.get('isYooMoney', False),
                    'is_telegram': doc.get('isTelegram', False),
                    'is_whats_app': doc.get('isWhatsApp', False),
                    'is_vk': doc.get('isVk', False),
                    'is_sberbank': doc.get('isSberbank', False),
                    'is_vtb_bank': doc.get('isVtbBank', False),
                    'is_tinkoff_bank': doc.get('isTinkoffBank', False),
                }
                
                # 移除 None 值的字段
                buyer_data = {k: v for k, v in buyer_data.items() if v is not None}
                
                # 插入数据到 PostgreSQL
                await BuyerInfo.create(**buyer_data)
                success_count += 1
                
                # 打印进度
                if idx % 100 == 0 or idx == total_records:
                    print(f"进度: {idx}/{total_records} ({(idx/total_records*100):.2f}%)")
                
            except Exception as e:
                error_count += 1
                print(f"错误 - 记录 {idx}: {str(e)}")
                continue
        
        print("\n迁移完成!")
        print(f"成功: {success_count}")
        print(f"失败: {error_count}")
        print(f"总计: {total_records}")
        
    except Exception as e:
        print(f"迁移过程中出错: {str(e)}")
    finally:
        try:
            if db.bind:
                print("正在关闭 PostgreSQL 连接...")
                await db.pop_bind().close()
            if 'mongo_client' in locals():
                print("正在关闭 MongoDB 连接...")
                mongo_client.close()
            print("数据库连接已关闭")
        except Exception as e:
            print(f"关闭连接时出错: {str(e)}")

if __name__ == "__main__":
    asyncio.run(migrate_data()) 