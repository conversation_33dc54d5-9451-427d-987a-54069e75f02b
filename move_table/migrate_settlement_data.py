import asyncio
import json
from datetime import datetime
from pymongo import MongoClient
from urllib.parse import quote_plus
from models.settlement_info import db, SettlementInfo

async def create_table():
    """创建结算信息表"""
    with open('hubstudio.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    postgres_config = config['postgresql']
    dsn = f"postgresql://{quote_plus(postgres_config['user'])}:{quote_plus(postgres_config['password'])}@{postgres_config['host']}:{postgres_config['port']}/{postgres_config['database']}"
    
    # 连接数据库并创建表
    await db.set_bind(dsn)
    await db.gino.create_all()
    
    print("表格创建完成")
    await db.pop_bind().close()

async def migrate_data():
    """迁移数据"""
    print("开始迁移数据...")
    
    # 读取配置
    with open('hubstudio.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # PostgreSQL 配置
    postgres_config = config['postgresql']
    dsn = f"postgresql://{quote_plus(postgres_config['user'])}:{quote_plus(postgres_config['password'])}@{postgres_config['host']}:{postgres_config['port']}/{postgres_config['database']}"
    
    # MongoDB 配置 - 硬编码
    mongo_username = quote_plus("root")
    mongo_password = quote_plus("AirSmile@1688")
    mongo_url = f"mongodb://{mongo_username}:{mongo_password}@************:27017"
    
    mongo_client = None
    
    try:
        # 连接 MongoDB
        mongo_client = MongoClient(mongo_url)
        db_mongo = mongo_client['review']
        
        # 测试 MongoDB 连接
        mongo_client.admin.command('ping')
        print("已成功连接到 MongoDB")
        
        # 获取所有文档
        collection = db_mongo['settlement']
        all_docs = list(collection.find())
        total_records = len(all_docs)
        print(f"找到 {total_records} 条记录")
        
        # 连接 PostgreSQL
        await db.set_bind(dsn)
        
        # 开始迁移
        success_count = 0
        error_count = 0
        
        for i, doc in enumerate(all_docs, 1):
            try:
                # 插入数据
                await SettlementInfo.create(
                    settlement_id=doc['settlementId'],
                    task_id=doc['taskId'],
                    amount=float(doc['amount']),
                    balance=float(doc['balance']),
                    item_type=doc['itemType'],
                    user_id=doc['userId'],
                    created_at=doc['createDate']
                )
                success_count += 1
                
                if i % 100 == 0 or i == len(all_docs):
                    print(f"进度: {i}/{len(all_docs)} ({(i/len(all_docs)*100):.2f}%)")
                    
            except Exception as e:
                error_count += 1
                print(f"错误 - 记录 {i}: {str(e)}")
                print(f"错误数据: {doc}")
        
        # 显示最终进度
        print(f"进度: {len(all_docs)}/{len(all_docs)} ({100:.2f}%)")
        
        # 显示统计信息
        print("\n迁移完成!")
        print(f"成功: {success_count}")
        print(f"失败: {error_count}")
        print(f"总计: {len(all_docs)}")
        
    except Exception as e:
        print(f"发生错误: {str(e)}")
    
    finally:
        # 关闭连接
        if mongo_client:
            print("正在关闭 MongoDB 连接...")
            mongo_client.close()
        
        await db.pop_bind().close()
        print("数据库连接已关闭")

async def main():
    await create_table()
    await migrate_data()

if __name__ == "__main__":
    asyncio.run(main()) 