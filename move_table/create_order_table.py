import asyncio
import json
from urllib.parse import quote_plus
from models.order_info import db

async def create_tables():
    """创建订单表"""
    print("开始连接数据库...")
    
    # 读取配置
    with open('hubstudio.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # PostgreSQL 配置
    postgres_config = config['postgresql']
    dsn = f"postgresql://{quote_plus(postgres_config['user'])}:{quote_plus(postgres_config['password'])}@{postgres_config['host']}:{postgres_config['port']}/{postgres_config['database']}"
    
    try:
        # 连接数据库
        await db.set_bind(dsn)
        print("已成功连接到数据库")
        
        # 创建review schema (如果不存在)
        await db.status(f"CREATE SCHEMA IF NOT EXISTS review")
        print("已创建review schema")
        
        # 删除旧表（如果存在）
        await db.status("DROP TABLE IF EXISTS review.tb_order_info")
        print("已删除旧表")
        
        # 创建新表
        await db.gino.create_all()
        print("已创建新表")
        
        # 添加表注释
        await db.status("COMMENT ON TABLE review.tb_order_info IS '订单信息表'")
        
        # 添加字段注释
        field_comments = {
            "id": "主键ID",
            "task_id": "任务ID",
            "platform": "平台标识",
            "product_id": "商品ID",
            "img": "商品图片URL",
            "price": "订单价格",
            "pre_date": "预定日期",
            "order_pcs": "订购数量",
            "keyword": "商品关键词",
            "is_like": "是否点赞",
            "is_cart": "是否加购",
            "is_review": "是否评论",
            "is_review_text": "是否文字评论",
            "is_review_img": "是否图片评论",
            "is_review_video": "是否视频评论",
            "review_text_input": "评论文字内容",
            "review_video_input": "评论视频文件名列表",
            "review_img_input": "评论图片文件名列表",
            "es_pay": "预计支付金额",
            "es_cost": "预计成本",
            "user_id": "用户ID",
            "order_id": "平台订单ID",
            "remark": "备注",
            "seller_id": "卖家ID",
            "buyer": "买家账号",
            "status": "订单状态",
            "created_at": "创建时间",
            "updated_at": "更新时间"
        }
        
        for field, comment in field_comments.items():
            await db.status(f"COMMENT ON COLUMN review.tb_order_info.{field} IS '{comment}'")
        
        print("添加索引...")
        # 已由模型定义添加的索引不需要重复添加
        
        print("表格创建完成")
    except Exception as e:
        print(f"发生错误: {str(e)}")
    finally:
        # 关闭数据库连接
        await db.pop_bind().close()
        print("数据库连接已关闭")

if __name__ == "__main__":
    asyncio.run(create_tables()) 