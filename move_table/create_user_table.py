import asyncio
import json
from models.user_info import db, UserInfo

async def drop_table():
    await db.status('DROP TABLE IF EXISTS review.tb_user_info CASCADE')

async def add_column_comments():
    comments = {
        'id': '主键ID',
        'user_id': '用户编号',
        'account': '登录账号',
        'password': '登录密码',
        'name': '用户名称',
        'role': '用户角色',
        'purchase_order_base_price': '采购订单基础价格',
        'purchase_order_like_price': '采购订单点赞价格',
        'purchase_order_add_on_price': '采购订单附加价格',
        'purchase_order_review_price': '采购订单评论价格',
        'unpurchased_order_like_price': '未采购订单点赞价格',
        'unpurchased_order_add_on_price': '未采购订单附加价格',
        'fee_discount': '费用折扣率',
        'balance': '账户余额',
        'created_at': '创建时间',
        'updated_at': '更新时间'
    }
    
    # 添加字段注释
    for column, comment in comments.items():
        try:
            await db.status(f"""
                COMMENT ON COLUMN review.tb_user_info.{column} IS '{comment}'
            """)
        except Exception as e:
            print(f"添加字段 {column} 的注释时出错: {str(e)}")

async def create_indexes():
    # 分别创建每个索引
    await db.status("""
        CREATE INDEX IF NOT EXISTS idx_user_info_user_id ON review.tb_user_info (user_id)
    """)
    await db.status("""
        CREATE INDEX IF NOT EXISTS idx_user_info_account ON review.tb_user_info (account)
    """)
    await db.status("""
        CREATE INDEX IF NOT EXISTS idx_user_info_role ON review.tb_user_info (role)
    """)

async def create_table():
    # 读取数据库配置
    with open('hubstudio.json', 'r') as f:
        config = json.load(f)
        db_config = config['postgresql']

    # 构建数据库 URL
    db_url = f"postgresql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}"
    
    try:
        print("正在连接数据库...")
        await db.set_bind(db_url)
        
        print("正在创建 review schema...")
        await db.status('CREATE SCHEMA IF NOT EXISTS review')
        
        print("正在删除旧表...")
        await drop_table()
        
        print("正在创建表...")
        await db.gino.create_all()
        
        print("正在创建索引...")
        await create_indexes()
        
        print("正在添加表注释...")
        await db.status("""
            COMMENT ON TABLE review.tb_user_info IS '用户信息表'
        """)
        
        print("正在添加字段注释...")
        await add_column_comments()
        
        print("表创建成功！")
        
        # 插入示例数据
        print("正在插入示例数据...")
        example_user = {
            'user_id': 'L0013',
            'account': 'nbyl',
            'password': '123456',
            'name': '宁波游龙',
            'role': 'user',
            'purchase_order_base_price': 18.00,
            'purchase_order_like_price': 1.00,
            'purchase_order_add_on_price': 1.00,
            'purchase_order_review_price': 7.00,
            'unpurchased_order_like_price': 3.00,
            'unpurchased_order_add_on_price': 3.00,
            'fee_discount': 1.00,
            'balance': 1623.18
        }
        
        await UserInfo.create(**example_user)
        print("示例数据插入成功！")
        
    except Exception as e:
        print(f"创建表时出错: {str(e)}")
    finally:
        if db.bind:
            print("正在关闭数据库连接...")
            await db.pop_bind().close()
            print("数据库连接已关闭")

if __name__ == "__main__":
    asyncio.run(create_table()) 