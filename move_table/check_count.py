import psycopg2

def count_records():
    # 连接到PostgreSQL数据库
    conn = psycopg2.connect(
        user='postgres',
        password='123456',
        database='review',
        host='localhost',
        port=5432
    )
    
    try:
        # 创建游标
        cur = conn.cursor()
        
        # 执行计数查询
        cur.execute('SELECT COUNT(*) FROM review.tb_settlement_info')
        count = cur.fetchone()[0]
        print(f'PostgreSQL中的记录数量: {count}')
        
        # 关闭游标
        cur.close()
    finally:
        # 关闭数据库连接
        conn.close()

if __name__ == '__main__':
    count_records() 