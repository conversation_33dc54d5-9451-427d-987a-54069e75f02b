import asyncio
import json
from datetime import datetime
from pymongo import MongoClient
from models.user_info import db, UserInfo
from urllib.parse import quote_plus

async def migrate_data():
    # 读取 PostgreSQL 配置
    with open('hubstudio.json', 'r') as f:
        config = json.load(f)
        db_config = config['postgresql']

    # 构建 PostgreSQL URL
    db_url = f"postgresql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}"
    
    try:
        print("正在连接 PostgreSQL...")
        await db.set_bind(db_url)
        
        # MongoDB 连接信息
        mongo_username = quote_plus("root")
        mongo_password = quote_plus("AirSmile@1688")
        mongo_host = "************"
        mongo_port = "27017"
        
        # 构建 MongoDB 连接 URL
        mongo_url = f"mongodb://{mongo_username}:{mongo_password}@{mongo_host}:{mongo_port}/"
        
        # 连接 MongoDB
        print("正在连接 MongoDB...")
        mongo_client = MongoClient(mongo_url)
        mongo_db = mongo_client['review']
        mongo_collection = mongo_db['userinfo']
        
        # 测试连接
        try:
            mongo_client.admin.command('ping')
            print("MongoDB 连接成功！")
        except Exception as e:
            raise Exception(f"MongoDB 连接失败: {str(e)}")
        
        # 获取所有 MongoDB 数据
        print("正在读取 MongoDB 数据...")
        mongo_data = list(mongo_collection.find())
        total_records = len(mongo_data)
        print(f"总共找到 {total_records} 条记录")
        
        # 迁移数据
        print("开始迁移数据...")
        success_count = 0
        error_count = 0
        
        for idx, doc in enumerate(mongo_data, 1):
            try:
                # 转换数据格式
                user_data = {
                    'user_id': doc.get('userId'),
                    'account': doc.get('account'),
                    'password': doc.get('password'),
                    'name': doc.get('name'),
                    'role': doc.get('role', 'user'),
                    'balance': float(doc.get('balance', 0)),
                }
                
                # 获取价格配置信息
                info = doc.get('info', {})
                if isinstance(info, dict):
                    user_data.update({
                        'purchase_order_base_price': float(info.get('PURCHASE_ORDER_BASE_PRICE', 0)),
                        'purchase_order_like_price': float(info.get('PURCHASE_ORDER_LIKE_PRICE', 0)),
                        'purchase_order_add_on_price': float(info.get('PURCHASE_ORDER_ADD_ON_PRICE', 0)),
                        'purchase_order_review_price': float(info.get('PURCHASE_ORDER_REVIEW_PRICE', 0)),
                        'unpurchased_order_like_price': float(info.get('UNPURCHASED_ORDER_ONLY_LIKE_PRICE', 0)),
                        'unpurchased_order_add_on_price': float(info.get('UNPURCHASED_ORDER_ONLY_ADD_ON_PRICE', 0)),
                        'fee_discount': float(info.get('FEE_DISCOUNT', 1))
                    })
                
                # 移除 None 值的字段
                user_data = {k: v for k, v in user_data.items() if v is not None}
                
                # 插入数据到 PostgreSQL
                await UserInfo.create(**user_data)
                success_count += 1
                
                # 打印进度
                if idx % 10 == 0 or idx == total_records:
                    print(f"进度: {idx}/{total_records} ({(idx/total_records*100):.2f}%)")
                
            except Exception as e:
                error_count += 1
                print(f"错误 - 记录 {idx}: {str(e)}")
                print(f"错误数据: {doc}")
                continue
        
        print("\n迁移完成!")
        print(f"成功: {success_count}")
        print(f"失败: {error_count}")
        print(f"总计: {total_records}")
        
    except Exception as e:
        print(f"迁移过程中出错: {str(e)}")
    finally:
        try:
            if db.bind:
                print("正在关闭 PostgreSQL 连接...")
                await db.pop_bind().close()
            if 'mongo_client' in locals():
                print("正在关闭 MongoDB 连接...")
                mongo_client.close()
            print("数据库连接已关闭")
        except Exception as e:
            print(f"关闭连接时出错: {str(e)}")

if __name__ == "__main__":
    asyncio.run(migrate_data()) 