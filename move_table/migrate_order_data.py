import asyncio
import json
from datetime import datetime
from pymongo import MongoClient
from urllib.parse import quote_plus
from models.order_info import db, OrderInfo

async def create_table():
    """创建订单信息表"""
    with open('hubstudio.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    postgres_config = config['postgresql']
    dsn = f"postgresql://{quote_plus(postgres_config['user'])}:{quote_plus(postgres_config['password'])}@{postgres_config['host']}:{postgres_config['port']}/{postgres_config['database']}"
    
    # 连接数据库并创建表
    await db.set_bind(dsn)
    await db.gino.create_all()
    
    print("表格创建完成")
    await db.pop_bind().close()

async def migrate_data():
    """迁移数据"""
    print("开始迁移数据...")
    
    # 读取配置
    with open('hubstudio.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # PostgreSQL 配置
    postgres_config = config['postgresql']
    dsn = f"postgresql://{quote_plus(postgres_config['user'])}:{quote_plus(postgres_config['password'])}@{postgres_config['host']}:{postgres_config['port']}/{postgres_config['database']}"
    
    # MongoDB 配置 - 硬编码
    mongo_username = quote_plus("root")
    mongo_password = quote_plus("AirSmile@1688")
    mongo_url = f"mongodb://{mongo_username}:{mongo_password}@************:27017"
    
    mongo_client = None
    
    try:
        # 连接 MongoDB
        mongo_client = MongoClient(mongo_url)
        db_mongo = mongo_client['review']
        
        # 测试 MongoDB 连接
        mongo_client.admin.command('ping')
        print("已成功连接到 MongoDB")
        
        # 获取所有文档
        collection = db_mongo['orders']
        all_docs = list(collection.find())
        total_records = len(all_docs)
        print(f"找到 {total_records} 条记录")
        
        # 连接 PostgreSQL
        await db.set_bind(dsn)
        
        # 开始迁移
        success_count = 0
        error_count = 0
        
        for i, doc in enumerate(all_docs, 1):
            try:
                # 安全地转换数值类型
                def safe_int(value, default=0):
                    if value is None or value == '':
                        return default
                    return int(value)
                
                def safe_float(value, default=0.0):
                    if value is None or value == '':
                        return default
                    return float(value)
                
                # 处理日期时间
                def safe_datetime(value):
                    if isinstance(value, datetime):
                        return value
                    return datetime.now()
                
                # 处理数组
                def safe_array(value):
                    if value is None:
                        return []
                    if isinstance(value, list):
                        return value
                    return []
                
                # 将整数值转换为布尔值
                def int_to_bool(value):
                    if value is None:
                        return False
                    return bool(safe_int(value, 0) > 0)

                # 插入数据
                await OrderInfo.create(
                    platform=doc['platform'],
                    product_id=safe_int(doc['productId']),
                    img=doc.get('img'),
                    price=safe_float(doc['price']),
                    pre_date=safe_datetime(doc.get('preDate')),
                    order_pcs=safe_int(doc.get('orderPcs', 1)),
                    keyword=doc.get('keyword'),
                    is_like=int_to_bool(doc.get('like')),
                    is_cart=int_to_bool(doc.get('cart')),
                    is_review=int_to_bool(doc.get('review')),
                    is_review_text=int_to_bool(doc.get('reviewText')),
                    is_review_img=int_to_bool(doc.get('reviewImg')),
                    is_review_video=int_to_bool(doc.get('reviewVideo')),
                    review_text_input=doc.get('reviewTextInput', ''),
                    review_video_input=safe_array(doc.get('reviewVideoInput')),
                    es_pay=safe_float(doc.get('esPay')),
                    es_cost=safe_float(doc.get('esCost')),
                    user_id=doc['userId'],
                    task_id=doc['taskId'],
                    order_id=doc.get('orderId') or None,  # 空字符串转为 None
                    remark=doc.get('remark', ''),
                    review_img_input=safe_array(doc.get('reviewImgInput')),
                    seller_id=doc.get('sellerId'),
                    buyer=doc.get('buyer'),
                    status=safe_int(doc.get('status')),
                    created_at=safe_datetime(doc['createTime']),
                    updated_at=safe_datetime(doc['updateTime'])
                )
                success_count += 1
                
                if i % 10 == 0 or i == len(all_docs):
                    print(f"进度: {i}/{len(all_docs)} ({(i/len(all_docs)*100):.2f}%)")
                    
            except Exception as e:
                error_count += 1
                print(f"错误 - 记录 {i}: {str(e)}")
                print(f"错误数据: {doc}")
        
        # 显示最终进度
        print(f"进度: {len(all_docs)}/{len(all_docs)} ({100:.2f}%)")
        
        # 显示统计信息
        print("\n迁移完成!")
        print(f"成功: {success_count}")
        print(f"失败: {error_count}")
        print(f"总计: {len(all_docs)}")
        
    except Exception as e:
        print(f"发生错误: {str(e)}")
    
    finally:
        # 关闭连接
        if mongo_client:
            print("正在关闭 MongoDB 连接...")
            mongo_client.close()
        
        await db.pop_bind().close()
        print("数据库连接已关闭")

async def main():
    await create_table()
    await migrate_data()

if __name__ == "__main__":
    asyncio.run(main()) 