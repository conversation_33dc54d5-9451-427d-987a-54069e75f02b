"""
HubStudio SDK 异步使用示例
"""
import os
import time
import asyncio
from contextlib import contextmanager
from typing import List
from hubstudio_sdk import (
    HubStudioClient,
    HubStudioError,
    Environment,
    BrowserStatus,
)
from datetime import datetime
from config import load_config, get_default_config_path
from server.proxy_server import MultiProxyManager
from logs import get_logger

# 创建logger实例
logger = get_logger("hubstudio_async")

async def handle_browser(container_code: int, client: HubStudioClient):
    """处理浏览器自动化任务"""
    try:
        # 使用start_playwright_async替代open_browser_async
        args=[
            "--start-maximized",#窗口最大化
            "--disable-extensions",#禁用扩展
            "--disable-blink-features=AutomationControlled"#禁用自动化控制
              ]
        browser_info, browser = await client.start_playwright_async(container_code=container_code,args=args)
        # 不需要使用with语句，直接使用返回的browser对象
        context = browser.contexts[0]
        page = context.pages[0]
        await page.goto("https://www.baidu.com")
        await page.wait_for_load_state("networkidle")
        
        # 截图
        now = datetime.now().strftime("%Y%m%d%H%M%S")
        filename = f"screenshot_{now}.png"
        await page.screenshot(path=filename)
        
        await asyncio.sleep(3)
        
        # 关闭页面
        await page.close()
        # 关闭browser
        await browser.close()
    except Exception as e:
        print(f"操作浏览器时发生错误: {str(e)}")

async def main():
    # 初始化多网卡代理管理器
    proxy_manager = MultiProxyManager(base_port=1080)
    num_proxies = proxy_manager.start_all()
    logger.info(f"启动了 {num_proxies} 个代理服务")
    
    # 获取默认配置文件路径
    config_path = get_default_config_path()
    
    # 加载配置文件
    config = load_config(config_path)
    
    # 使用配置初始化客户端
    client = HubStudioClient(config=config)
    
    try:
        # 获取环境列表
        envs = client.get_environments()
        if not envs:
            print("没有可用的环境")
            return
        
        # 选择第一个环境
        env = envs[0]
        print(f"选择环境: {env.container_name} (ID: {env.container_code})")
        
        # 打开浏览器并处理任务
        await handle_browser(env.container_code, client)
        
    except HubStudioError as e:
        print(f"发生错误: {e}")
    finally:
        # 关闭客户端连接
        client.stop()
        # 停止所有代理服务
        proxy_manager.stop_all()

if __name__ == "__main__":
    logger.info("程序开始运行")
    asyncio.run(main())
