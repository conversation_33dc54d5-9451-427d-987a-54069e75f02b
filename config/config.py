"""
配置文件加载模块
"""
import os
import json
from typing import Dict, Any, Optional
import logging

# 获取日志记录器
logger = logging.getLogger("Hubstudio异步")

def load_config(config_path: str = "config.json") -> Dict[str, Any]:
    """加载配置文件
    
    Args:
        config_path: 配置文件路径，默认为config.json
        
    Returns:
        配置文件内容字典
    """
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        logger.info(f"成功加载配置文件: {config_path}")
        return config
    except Exception as e:
        logger.error(f"加载配置文件失败: {str(e)}")
        raise

def get_config_value(config: Dict[str, Any], key: str, default: Optional[Any] = None) -> Any:
    """从配置中获取特定键的值
    
    Args:
        config: 配置字典
        key: 要获取的键名
        default: 如果键不存在时返回的默认值
        
    Returns:
        配置值或默认值
    """
    return config.get(key, default)

def get_default_config_path() -> str:
    """获取默认配置文件路径
    
    Returns:
        默认配置文件的绝对路径
    """
    # 获取项目根目录路径
    root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    return os.path.join(root_dir, "config.json") 