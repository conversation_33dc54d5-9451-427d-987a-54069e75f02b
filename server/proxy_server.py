import socket
import threading
import struct
import select
import psutil
from logs import get_logger

# 创建日志记录器
logger = get_logger("proxy_server")

class ProxyServer:
    def __init__(self, iface_name, port=1080, debug=False):
        self.iface_name = iface_name
        self.port = port
        self.server = None
        self.thread = None
        self.running = False
        self.debug = debug
        self.connection_count = 0
        self.active_connections = 0
        self.failed_connections = 0
        
    def set_debug(self, debug=True):
        """启用或禁用调试日志"""
        self.debug = debug
        
    def _debug_log(self, message):
        """根据调试模式输出日志"""
        if self.debug:
            logger.debug(message)
    
    def get_stats(self):
        """获取连接统计信息"""
        return {
            "total_connections": self.connection_count,
            "active_connections": self.active_connections,
            "failed_connections": self.failed_connections,
            "interface": self.iface_name,
            "port": self.port,
            "running": self.running
        }
        
    def get_interface_ip(self):
        """获取指定网卡的IP地址"""
        net_if_addrs = psutil.net_if_addrs()
        if self.iface_name in net_if_addrs:
            for iface in net_if_addrs[self.iface_name]:
                if iface.family == socket.AF_INET:
                    return iface.address
        return None
        
    def handle_client(self, client, address):
        """处理SOCKS5客户端连接"""
        remote = None
        self.connection_count += 1
        self.active_connections += 1
        
        try:
            # 设置超时，防止客户端不响应
            client.settimeout(30)
            
            # 1. 认证方法协商
            try:
                data = client.recv(2)
                if not data or len(data) < 2:
                    logger.debug("客户端握手数据不完整")
                    return
                ver, nmethods = struct.unpack("!BB", data)
                if ver != 5:  # SOCKS5
                    logger.debug(f"不支持的SOCKS版本: {ver}")
                    return
                    
                methods = client.recv(nmethods)
                
                # 回复客户端使用无认证方式
                client.send(struct.pack("!BB", 0x05, 0x00))
            except (socket.timeout, ConnectionResetError, OSError) as e:
                logger.debug(f"认证阶段失败: {e}")
                return
            
            # 重置超时
            client.settimeout(30)
            
            # 2. 接收代理请求
            try:
                data = client.recv(4)
                if not data or len(data) < 4:
                    logger.debug("代理请求数据不完整")
                    return
                    
                ver, cmd, rsv, atyp = struct.unpack("!BBBB", data)
                
                if cmd != 1:  # CONNECT
                    logger.debug(f"不支持的命令: {cmd}")
                    client.send(struct.pack("!BBBBIH", 0x05, 0x07, 0x00, 0x01, 0, 0))  # Command not supported
                    return
                
                if atyp == 1:  # IPv4
                    addr_data = client.recv(4)
                    if len(addr_data) < 4:
                        return
                    dst_addr = socket.inet_ntoa(addr_data)
                elif atyp == 3:  # Domain name
                    addr_len_data = client.recv(1)
                    if not addr_len_data:
                        return
                    addr_len = addr_len_data[0]
                    addr_data = client.recv(addr_len)
                    if len(addr_data) < addr_len:
                        return
                    dst_addr = addr_data.decode('ascii', errors='ignore')
                else:
                    logger.debug(f"不支持的地址类型: {atyp}")
                    client.send(struct.pack("!BBBBIH", 0x05, 0x08, 0x00, 0x01, 0, 0))  # Address type not supported
                    return
                    
                port_data = client.recv(2)
                if len(port_data) < 2:
                    return
                dst_port = struct.unpack('!H', port_data)[0]
                
                logger.debug(f"请求连接: {dst_addr}:{dst_port}")
            except (socket.timeout, ConnectionResetError, OSError) as e:
                logger.debug(f"解析请求阶段失败: {e}")
                return
            
            # 使用指定网卡IP作为源地址
            try:
                source_ip = self.get_interface_ip()
                if not source_ip:
                    logger.error(f"无法获取网卡 {self.iface_name} 的IP地址")
                    client.send(struct.pack("!BBBBIH", 0x05, 0x04, 0x00, 0x01, 0, 0))  # Host unreachable
                    return
                
                # 创建连接到目标的socket
                remote = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                remote.settimeout(30)  # 设置连接超时
                remote.bind((source_ip, 0))
                
                # 尝试连接目标
                try:
                    remote.connect((dst_addr, dst_port))
                except (socket.timeout, ConnectionRefusedError) as e:
                    logger.debug(f"连接目标失败: {dst_addr}:{dst_port} - {e}")
                    client.send(struct.pack("!BBBBIH", 0x05, 0x05, 0x00, 0x01, 0, 0))  # Connection refused
                    return
                except socket.gaierror as e:
                    logger.debug(f"DNS解析失败: {dst_addr} - {e}")
                    client.send(struct.pack("!BBBBIH", 0x05, 0x04, 0x00, 0x01, 0, 0))  # Host unreachable
                    return
                
                bind_address = remote.getsockname()
                addr = struct.unpack("!I", socket.inet_aton(bind_address[0]))[0]
                port = bind_address[1]
                
                # 发送成功连接响应
                reply = struct.pack("!BBBBIH", 0x05, 0x00, 0x00, 0x01, addr, port)
                client.send(reply)
                
                # 连接建立成功后移除超时
                client.settimeout(None)
                remote.settimeout(None)
                
                # 3. 开始转发数据
                self.forward_data(client, remote)
                
            except Exception as e:
                logger.error(f"连接阶段发生错误: {e}")
                try:
                    # 发送通用错误响应
                    client.send(struct.pack("!BBBBIH", 0x05, 0x01, 0x00, 0x01, 0, 0))
                except:
                    pass
                
        except Exception as e:
            error_type = type(e).__name__
            if "10049" in str(e):
                pass
            if "10054" in str(e):
                logger.debug(f"远程主机关闭连接: {error_type} - {e}")
            else:
                logger.error(f"处理客户端连接错误: {error_type} - {e}")
        finally:
            # 减少活动连接计数
            self.active_connections -= 1
            
            # 确保客户端socket关闭
            try:
                if client:
                    client.close()
            except:
                pass
            # 确保远程socket关闭
            try:
                if remote:
                    remote.close()
            except:
                pass
            
    def forward_data(self, client, remote, buffer_size=4096):
        """在客户端和远程服务器之间转发数据"""
        try:
            while True:
                # 设置超时，避免无限等待
                r, w, e = select.select([client, remote], [], [], 60)
                
                if not r:  # 超时
                    continue
                    
                if client in r:
                    try:
                        data = client.recv(buffer_size)
                        if not data:
                            break
                        remote.send(data)
                    except (ConnectionResetError, ConnectionAbortedError, socket.error) as e:
                        logger.debug(f"客户端连接中断: {e}")
                        break
                    
                if remote in r:
                    try:
                        data = remote.recv(buffer_size)
                        if not data:
                            break
                        client.send(data)
                    except (ConnectionResetError, ConnectionAbortedError, socket.error) as e:
                        logger.debug(f"远程服务器连接中断: {e}")
                        break
        except Exception as e:
            logger.debug(f"数据转发异常: {e}")
        finally:
            # 确保连接关闭
            try:
                client.close()
            except:
                pass
            try:
                remote.close()
            except:
                pass
            
    def start(self):
        """启动代理服务器"""
        def run():
            self.server = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server.bind(('127.0.0.1', self.port))
            self.server.listen(5)
            self.running = True
            
            logger.info(f'SOCKS5代理服务已启动在端口 {self.port}，使用网卡 {self.iface_name}')
            
            while self.running:
                try:
                    client, addr = self.server.accept()
                    t = threading.Thread(target=self.handle_client, args=(client, addr))
                    t.daemon = True
                    t.start()
                except Exception as e:
                    if self.running:
                        logger.error(f"接受连接错误: {str(e)}")
                        
        self.thread = threading.Thread(target=run)
        self.thread.daemon = True
        self.thread.start()
        
    def stop(self):
        """停止代理服务器"""
        self.running = False
        if self.server:
            try:
                self.server.close()
            except:
                pass
        if self.thread:
            self.thread.join()
        logger.info(f'代理服务已停止: {self.iface_name}')


class MultiProxyManager:
    """多网卡代理管理器"""
    
    def __init__(self, base_port=1080, debug=False):
        """
        初始化多网卡代理管理器
        
        Args:
            base_port: 基础端口号，每个网卡会递增使用端口
            debug: 是否启用调试日志
        """
        self.base_port = base_port
        self.proxy_servers = {}  # {网卡名称: ProxyServer实例}
        self.debug = debug
        
    def set_debug(self, debug=True):
        """设置调试模式"""
        self.debug = debug
        for proxy in self.proxy_servers.values():
            proxy.set_debug(debug)
            
    def get_network_interfaces(self):
        """获取所有可用的网络接口"""
        interfaces = []
        net_if_addrs = psutil.net_if_addrs()
        
        for iface_name, addrs in net_if_addrs.items():
            # 确认接口有IPv4地址
            for addr in addrs:
                if addr.family == socket.AF_INET:
                    interfaces.append(iface_name)
                    break
                    
        return interfaces
        
    def start_all(self):
        """启动所有网卡的代理服务"""
        interfaces = self.get_network_interfaces()
        logger.info(f"发现 {len(interfaces)} 个网络接口")
        
        for i, iface_name in enumerate(interfaces):
            port = self.base_port + i
            logger.info(f"为接口 {iface_name} 创建代理，端口: {port}")
            
            proxy = ProxyServer(iface_name, port, debug=self.debug)
            proxy.start()
            self.proxy_servers[iface_name] = proxy
            
        return len(self.proxy_servers)
        
    def stop_all(self):
        """停止所有代理服务"""
        for iface_name, proxy in self.proxy_servers.items():
            logger.info(f"停止接口 {iface_name} 的代理服务")
            proxy.stop()
            
        self.proxy_servers.clear()
        
    def get_proxy_info(self):
        """获取所有代理服务的信息"""
        result = []
        for iface_name, proxy in self.proxy_servers.items():
            ip = proxy.get_interface_ip()
            stats = proxy.get_stats()
            info = {
                "interface": iface_name,
                "port": proxy.port,
                "ip": ip,
                "running": proxy.running,
                "connections": stats
            }
            result.append(info)
        return result
    
    def get_proxy_for_interface(self, interface_name):
        """获取指定网卡的代理服务器"""
        return self.proxy_servers.get(interface_name)


# 使用示例
if __name__ == "__main__":
    # 单网卡使用
    # proxy = ProxyServer("WLAN2", 1080)
    # proxy.start()
    
    # 多网卡使用
    manager = MultiProxyManager(base_port=1080)
    num_started = manager.start_all()
    print(manager.get_proxy_info())
    
    print(f"启动了 {num_started} 个代理服务器")
    print("代理信息:")
    for info in manager.get_proxy_info():
        print(f"  接口: {info['interface']}, IP: {info['ip']}, 端口: {info['port']}")
    
    try:
        # 保持程序运行
        import time
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        # 按Ctrl+C退出
        manager.stop_all()
        print("已停止所有代理服务") 