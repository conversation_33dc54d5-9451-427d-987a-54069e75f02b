"""
统一日志管理模块

提供统一的日志记录功能，支持控制台和文件输出，以及不同级别的日志记录。
"""
import os
import logging
import logging.config
from typing import Optional, Dict, Any, Union


# 日志记录器缓存
_loggers = {}

class PyWifiFilter(logging.Filter):
    def filter(self, record):
        return "pywifi" not in record.name  # 如果日志名包含 "pywifi"，则过滤掉
    
def get_logger(name: str = "") -> logging.Logger:
    """
    获取或创建一个日志记录器。
    
    如果日志记录器已存在，则返回缓存的实例，否则创建一个新的实例。
    
    Args:
        name: 日志记录器名称
        
    Returns:
        日志记录器实例
    """
    if name in _loggers:
        return _loggers[name]
    
    # 创建新的日志记录器
    logger = setup_logger(name)
    _loggers[name] = logger
    return logger


def setup_logger(
    name: str = "",
    level: str = "INFO",
    log_file: Optional[str] = None,
    log_format: Optional[str] = None,
    propagate: bool = False,
) -> logging.Logger:
    """
    配置日志记录器。
    
    Args:
        name: 日志记录器名称
        level: 日志级别，默认为"INFO"
        log_file: 日志文件路径，如果提供则同时输出到文件
        log_format: 日志格式，如果不提供则使用默认格式
        propagate: 是否传播日志到父记录器，默认为False
        
    Returns:
        配置好的日志记录器
    """
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, level.upper()))
    logger.propagate = propagate

    # 添加过滤器以过滤掉包含 "pywifi" 的日志
    logger.addFilter(PyWifiFilter())
    
    # 清除现有处理器
    if logger.handlers:
        logger.handlers.clear()
    
    # 默认日志格式
    if log_format is None:
        log_format = (
            "%(asctime)s [%(levelname)s] %(name)s: "
            "%(message)s (%(filename)s:%(lineno)d)"
        )
    formatter = logging.Formatter(log_format)
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 文件处理器
    if log_file:
        # 确保日志目录存在
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir)
            
        file_handler = logging.FileHandler(
            log_file,
            encoding="utf-8"
        )
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger


def configure_logging(config: Dict[str, Any]) -> None:
    """
    使用配置字典配置日志系统。
    
    Args:
        config: 日志配置字典，符合logging.config.dictConfig格式
    """
    logging.config.dictConfig(config)


def configure_logging_from_file(config_file: str) -> None:
    """
    从配置文件加载日志配置。
    
    Args:
        config_file: 配置文件路径，可以是JSON或YAML格式
    """
    import json
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        configure_logging(config)
    except Exception as e:
        # 如果配置失败，使用默认配置
        print(f"加载日志配置文件失败: {e}")
        logger = setup_logger("root", "INFO")
        logger.error(f"加载日志配置文件失败: {e}")


# 初始化基本日志配置
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s"
) 